module.exports = {
  env: {
    node: true,
    commonjs: true,
    es2021: true,
    jest: true
  },
  extends: [
    'eslint:recommended'
  ],
  parserOptions: {
    ecmaVersion: 'latest'
  },
  rules: {
    // Quy tắc về lỗi tiềm ẩn
    'no-await-in-loop': 'warn',
    'no-console': 'warn', // Ưu tiên sử dụng logger thay vì console
    'no-constant-binary-expression': 'error',
    'no-promise-executor-return': 'error',
    'no-template-curly-in-string': 'warn',
    'no-unreachable-loop': 'error',
    'require-atomic-updates': 'error',
    
    // Quy tắc về thực tiễn tốt nhất
    'array-callback-return': 'error',
    'block-scoped-var': 'error',
    'camelcase': ['warn', { properties: 'never' }],
    'complexity': ['warn', { max: 15 }],
    'consistent-return': 'warn',
    'default-param-last': 'error',
    'dot-notation': 'warn',
    'eqeqeq': 'error',
    'max-classes-per-file': ['warn', 1],
    'max-depth': ['warn', 4],
    'max-lines': ['warn', { max: 500, skipBlankLines: true, skipComments: true }],
    'max-params': ['warn', 4],
    'no-alert': 'error',
    'no-eval': 'error',
    'no-extend-native': 'error',
    'no-implicit-coercion': 'warn',
    'no-implied-eval': 'error',
    'no-invalid-this': 'error',
    'no-lone-blocks': 'error',
    'no-new-func': 'error',
    'no-param-reassign': 'warn',
    'no-return-assign': 'error',
    'no-throw-literal': 'error',
    'no-unused-expressions': 'error',
    'no-useless-concat': 'warn',
    'no-useless-return': 'warn',
    'prefer-arrow-callback': 'warn',
    'prefer-const': 'warn',
    'prefer-promise-reject-errors': 'error',
    'require-await': 'warn',
    
    // Quy tắc về phong cách
    'array-bracket-spacing': ['warn', 'never'],
    'block-spacing': 'warn',
    'brace-style': ['warn', '1tbs'],
    'comma-dangle': ['warn', 'never'],
    'comma-spacing': ['warn', { before: false, after: true }],
    'comma-style': ['warn', 'last'],
    'func-call-spacing': ['warn', 'never'],
    'indent': ['warn', 2, { SwitchCase: 1 }],
    'key-spacing': ['warn', { beforeColon: false, afterColon: true }],
    'linebreak-style': ['error', 'unix'],
    'max-len': ['warn', { code: 120, ignoreComments: true, ignoreUrls: true, ignoreStrings: true }],
    'no-multi-spaces': 'warn',
    'no-multiple-empty-lines': ['warn', { max: 2, maxEOF: 1 }],
    'no-trailing-spaces': 'warn',
    'quotes': ['warn', 'single', { avoidEscape: true }],
    'semi': ['error', 'always'],
    'semi-spacing': ['warn', { before: false, after: true }],
    'sort-imports': ['warn', { ignoreDeclarationSort: true }],
    'space-before-blocks': 'warn',
    'space-before-function-paren': ['warn', { anonymous: 'always', named: 'never', asyncArrow: 'always' }],
    'space-in-parens': ['warn', 'never'],
    'space-infix-ops': 'warn',
    
    // Các quy tắc ECMAScript 6
    'arrow-body-style': ['warn', 'as-needed'],
    'arrow-parens': ['warn', 'as-needed'],
    'arrow-spacing': ['warn', { before: true, after: true }],
    'no-var': 'error',
    'prefer-rest-params': 'warn',
    'prefer-spread': 'warn',
    'prefer-template': 'warn',
    'rest-spread-spacing': ['warn', 'never']
  }
}; 