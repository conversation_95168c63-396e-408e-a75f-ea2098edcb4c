## 0. <PERSON><PERSON><PERSON> đích và Đối tượng của Tài liệu

Tài liệu này là kim chỉ nam cho mọi hoạt động **phát triển code của bot Discord** trong dự án Ông Ba Dạy Hoá. Đ<PERSON>i tượng chính của tài liệu là các AI Agent và Developer tham gia vào việc viết code.

**Mục tiêu:**
*   Đảm bảo tất cả code được phát triển một cách nhất quán, tuân thủ các tiêu chuẩn chất lượng cao.
*   Giúp các tính năng được thiết kế và triển khai một cách module hóa, dễ bảo trì và mở rộng.
*   Cung cấp đủ thông tin và bối cảnh để AI Agent và Developer viết code tương thích tốt với quy trình và môi trường triển khai đã định sẵn.

**Hướng dẫn sử dụng cho AI Agent và Developer:**
*   Trước khi bắt đầu bất kỳ nhiệm vụ nào liên quan đến việc viết hoặc chỉnh sửa code (tạo tính năng mới, sửa lỗi, refactor), vui lòng **tham khảo kỹ các quy tắc và hướng dẫn liên quan** trong tài liệu này.
*   **Vai trò chính của bạn là phát triển và hoàn thiện code cho các tính năng của bot Discord.**
*   Các thông tin chi tiết về quy trình triển khai và vận hành (ví dụ: Mục 17 về Dokploy) được cung cấp chủ yếu để bạn **hiểu rõ bối cảnh hệ thống và môi trường mà bot sẽ hoạt động.** Điều này giúp bạn đưa ra các quyết định thiết kế và viết code phù hợp, tránh tạo ra các thay đổi có thể gây xung đột hoặc khó khăn cho quá trình triển khai trên Dokploy do Product Owner/Quản trị viên thực hiện.
*   **Bạn không chịu trách nhiệm thực hiện các bước triển khai (deploy) bot.**
*   Nếu có bất kỳ thắc mắc nào về các quy tắc, cách chúng ảnh hưởng đến việc code của bạn, hoặc nếu bạn có đề xuất cải thiện tài liệu, vui lòng trao đổi với Product Owner.

# Nguyên tắc Phát triển Bot Discord theo Cấu trúc Tính năng

## 1. Tổng quan về Cấu trúc Thư mục

Dự án này sử dụng cấu trúc thư mục theo tính năng để đảm bảo tính module hóa, dễ bảo trì và mở rộng.

```
src/
├── features/                # Chứa code cho từng tính năng cụ thể
│   ├── [feature_name]/      # Thư mục cho một tính năng (ví dụ: streak, course)
│   │   ├── commands/        # Lệnh Discord liên quan đến tính năng
│   │   │   └── ... (tên file lệnh)
│   │   ├── services/        # Logic nghiệp vụ của tính năng
│   │   │   └── ... (tên file service)
│   │   ├── models/          # (Tùy chọn) Định nghĩa model/schema dữ liệu của tính năng
│   │   │   └── ... (tên file model)
│   │   ├── events/          # (Tùy chọn) Xử lý sự kiện Discord liên quan đến tính năng
│   │   │   └── ... (tên file event)
│   │   ├── utils/           # (Tùy chọn) Các hàm tiện ích chỉ dành riêng cho tính năng này
│   │   │   └── ... (tên file util)
│   │   └── index.js         # (Tùy chọn) Export các thành phần chính của tính năng
│   └── ...                  # Các tính năng khác
├── core/                    # Các thành phần cốt lõi của bot
│   ├── bot.js               # Khởi tạo và cấu hình chính của bot
│   ├── client.js            # Cấu hình Discord client
│   └── config/              # Các file cấu hình (database, bot token, etc.)
│       ├── index.js
│       └── ...
├── shared/                  # Code được chia sẻ giữa nhiều tính năng
│   ├── database/            # Cấu hình, kết nối, và các query/model dùng chung
│   │   ├── connection.js
│   │   └── models/          # Models dùng chung (nếu có)
│   │       └── ...
│   ├── utils/               # Các hàm tiện ích chung
│   │   ├── logger.js
│   │   └── ...
│   ├── middleware/          # Middleware dùng chung (nếu có)
│   │   └── ...
│   └── types/               # (Nếu dùng TypeScript) Định nghĩa kiểu dữ liệu chung
│       └── index.d.ts
├── events/                  # Các event handler chung, không thuộc tính năng cụ thể
│   ├── ready.js
│   └── messageCreate.js     # (Ví dụ, nếu xử lý prefix chung)
├── commands/                # (Tùy chọn) Các lệnh chung, không thuộc tính năng cụ thể
│   └── general/
│       └── help.js
└── index.js                 # Điểm vào chính của ứng dụng

tests/
├── features/
│   ├── [feature_name]/
│   │   ├── commands.test.js
│   │   └── services.test.js
│   └── ...
├── shared/
│   └── utils.test.js
└── ...
```

## 2. Nguyên tắc Phát triển Tính năng Mới

Khi phát triển một tính năng mới (ví dụ: "polls"):

1.  **Tạo thư mục tính năng:**
    *   Tạo một thư mục mới trong `src/features/` với tên_tính_năng (ví dụ: `src/features/polls/`).
    *   Tên thư mục phải là chữ thường, dùng dấu gạch dưới nếu cần (snake_case).

2.  **Cấu trúc bên trong thư mục tính năng:**
    *   `README.md`: **BẮT BUỘC.** File này mô tả chi tiết về tính năng.
        *   **Nội dung yêu cầu:**
            *   Mô tả tổng quan: Tính năng này làm gì, mục đích là gì?
            *   Các lệnh chính và cách sử dụng.
            *   Luồng hoạt động chính (flowchart bằng Mermaid Syntax - Tiếng Anh).
            *   Các User Cases quan trọng và cách xử lý (sequence diagram hoặc use case diagram bằng Mermaid Syntax - Tiếng Anh).
            *   Bất kỳ lưu ý kỹ thuật hoặc dependencies đặc biệt nào.
        *   Ví dụ về Mermaid flowchart:
            ```mermaid
            graph TD
                A[User executes /poll command] --> B{Is input valid?};
                B -- Yes --> C[PollService.createPoll];
                C --> D[Database.savePoll];
                D --> E[Bot sends poll message];
                B -- No --> F[Bot replies with error];
            ```
    *   `commands/`: Chứa các file lệnh. Mỗi lệnh nên là một file riêng.
        *   Ví dụ: `src/features/polls/commands/create_poll.js`, `src/features/polls/commands/vote.js`.
    *   `services/`: Chứa logic nghiệp vụ. Tách logic phức tạp ra thành các services.
        *   Ví dụ: `src/features/polls/services/poll_service.js`.
    *   `models/` (Nếu cần): Định nghĩa schema/model dữ liệu cho tính năng.
        *   Ví dụ: `src/features/polls/models/poll_model.js`.
    *   `events/` (Nếu cần): Xử lý các sự kiện Discord đặc thù cho tính năng này.
        *   Ví dụ: `src/features/polls/events/reaction_add_event.js` (nếu vote bằng reaction).
    *   `utils/` (Nếu cần): Các hàm tiện ích *chỉ sử dụng trong tính năng này*.
        *   Ví dụ: `src/features/polls/utils/format_poll_results.js`.
    *   `index.js` (Tùy chọn): File này có thể export các thành phần chính của tính năng để dễ dàng import từ bên ngoài (ví dụ: từ file đăng ký lệnh chính).

3.  **Đăng ký Lệnh và Sự kiện:**
    *   Các lệnh của tính năng phải được đăng ký với bot. Cơ chế đăng ký có thể nằm trong `core/bot.js` hoặc một file quản lý lệnh trung tâm, và nó sẽ duyệt qua các thư mục tính năng.
    *   Tương tự, các event handler của tính năng cũng cần được đăng ký.

4.  **Tính Độc Lập:**
    *   **Ưu tiên:** Giữ code của tính năng càng độc lập càng tốt.
    *   **Hạn chế:** Tránh gọi trực tiếp service hoặc model của tính năng khác. Nếu cần tương tác, hãy xem xét việc sử dụng events hoặc một service chung trong `shared/`.
    *   Code trong một thư mục `features/[feature_name]/` **KHÔNG** được import trực tiếp từ một thư mục `features/[another_feature_name]/` khác.

## 3. Sử dụng Thư mục `shared/`

1.  **Mục đích:** `shared/` chứa code được sử dụng lại bởi **nhiều hơn một tính năng**, hoặc các thành phần cốt lõi của hệ thống.
    *   `shared/database/`: Kết nối database, các model/schema chung, query builders chung.
    *   `shared/utils/`: Các hàm tiện ích phổ biến (ví dụ: `logger.js`, `permission_checker.js`, `date_formatter.js`).
    *   `shared/middleware/`: Middleware cho các lệnh (ví dụ: kiểm tra quyền, logging request).
    *   `shared/types/`: Định nghĩa kiểu TypeScript dùng chung.

2.  **Khi nào thêm vào `shared/`:**
    *   Khi một đoạn code (utility, service, model) được sử dụng bởi ít nhất **hai tính năng khác nhau**.
    *   Khi một chức năng là nền tảng cho nhiều phần của bot (ví dụ: logging, cấu hình).

3.  **Cẩn trọng:** Không lạm dụng `shared/`. Nếu một utility chỉ phục vụ một tính năng, nó nên nằm trong thư mục `utils/` của tính năng đó.

## 4. Nguyên tắc Viết Test Case

Mục tiêu là đảm bảo mỗi tính năng hoạt động đúng và việc thay đổi không làm hỏng các phần khác.

1.  **Vị trí Test:**
    *   Test cho một tính năng cụ thể nên được đặt trong `tests/features/[feature_name]/`.
        *   Ví dụ: `tests/features/polls/commands.test.js`, `tests/features/polls/services.test.js`.
    *   Test cho code trong `shared/` nên được đặt trong `tests/shared/`.
        *   Ví dụ: `tests/shared/utils.test.js`.

2.  **Loại Test:**
    *   **Unit Tests:** Tập trung vào việc kiểm thử các đơn vị code nhỏ nhất (hàm, class method) một cách độc lập. Mock các dependencies bên ngoài (ví dụ: Discord client, database access).
        *   Ví dụ: Test một hàm trong `poll_service.js` để đảm bảo nó tính toán kết quả poll chính xác dựa trên input giả định.
    *   **Integration Tests (Mức độ tính năng):** Kiểm thử sự tương tác giữa các thành phần trong cùng một tính năng (ví dụ: command gọi service, service tương tác model).
        *   Ví dụ: Test luồng tạo poll từ command, qua service, và lưu vào database (có thể dùng test database).
    *   **KHÔNG** thực hiện E2E test kết nối trực tiếp đến Discord API trong unit/integration test tự động thường xuyên. Có thể có một bộ E2E test riêng chạy thủ công hoặc ít thường xuyên hơn.

3.  **Nội dung và Quy trình Viết Unit Test Chi tiết:**
    *   **Cấu trúc Test (Arrange-Act-Assert - AAA):** Luôn tuân theo cấu trúc này.
        *   **Arrange:** Khởi tạo đối tượng, thiết lập dữ liệu đầu vào, mock các dependencies cần thiết.
            *   Ví dụ: Tạo một instance của service, chuẩn bị dữ liệu đầu vào cho hàm, mock hàm gọi database để trả về dữ liệu giả định.
        *   **Act:** Gọi hàm hoặc phương thức đang được kiểm thử với các tham số đã chuẩn bị.
        *   **Assert:** Kiểm tra xem kết quả trả về, trạng thái của đối tượng, hoặc các lời gọi đến hàm mock có đúng như mong đợi không.
    *   **Tên Test Case:**
        *   Phải rõ ràng, mô tả hành vi được kiểm thử và kết quả mong đợi.
        *   Ví dụ: `should create poll successfully with valid data`, `should return error when poll name is missing`, `should correctly calculate_streak_days_for_user`.
    *   **Mocking và Stubbing:**
        *   Sử dụng thư viện mocking (ví dụ: Jest mocks) để cô lập unit đang test.
        *   Mock tất cả các external dependencies như Discord API client, database services, other services.
        *   Chỉ test logic của unit đó, không test logic của dependencies.
    *   **Độ bao phủ (Coverage):**
        *   Cố gắng đạt độ bao phủ test cao cho các logic nghiệp vụ quan trọng, đặc biệt là trong `services/`.
        *   Kiểm tra cả "happy path" và các "edge cases" (input không hợp lệ, lỗi, giá trị biên).
    *   **Mỗi `service` và `command` quan trọng đều PHẢI có unit test tương ứng.**
    *   **Ví dụ Unit Test (Giả định dùng Jest):**
        ```javascript
        // tests/features/streak/services/streak_service.test.js
        const StreakService = require('../../../../src/features/streak/services/streak_service');
        const mockUserModel = { findOne: jest.fn(), save: jest.fn() };

        describe('StreakService', () => {
          let streakService;

          beforeEach(() => {
            // Arrange: Reset mocks for each test
            mockUserModel.findOne.mockReset();
            mockUserModel.save.mockReset();
            streakService = new StreakService(mockUserModel); // Giả sử UserModel được inject
          });

          it('should increment streak for existing user', async () => {
            // Arrange
            const userId = 'user123';
            const mockUser = { userId, currentStreak: 5, lastStreakDate: new Date(Date.now() - 24 * 60 * 60 * 1000) };
            mockUserModel.findOne.mockResolvedValue({ ...mockUser, save: mockUserModel.save }); // Giả lập Mongoose model instance

            // Act
            const updatedUser = await streakService.incrementStreak(userId);

            // Assert
            expect(mockUserModel.findOne).toHaveBeenCalledWith({ userId });
            expect(mockUserModel.save).toHaveBeenCalled();
            expect(updatedUser.currentStreak).toBe(6);
          });

          it('should return error if user not found', async () => {
            // Arrange
            const userId = 'nonExistentUser';
            mockUserModel.findOne.mockResolvedValue(null);

            // Act & Assert
            await expect(streakService.incrementStreak(userId)).rejects.toThrow('User not found');
          });
        });
        ```

4.  **Công cụ:**
    *   Sử dụng một testing framework phổ biến (ví dụ: Jest, Mocha & Chai).
    *   Sử dụng thư viện mocking (ví dụ: Jest mocks, Sinon.JS).

5.  **Khi nào viết Test:**
    *   **Lý tưởng:** Viết test song song hoặc ngay sau khi viết code cho một chức năng.
    *   **Bắt buộc:** Trước khi merge code của một tính năng mới hoặc một thay đổi lớn vào nhánh chính.

6.  **Đảm bảo Test Pass:**
    *   Tất cả các test phải pass trước khi deploy lên production.
    *   Thiết lập CI/CD pipeline để tự động chạy test khi có commit mới.

## 5. Quy ước Đặt tên

*   **Thư mục:** `snake_case` (ví dụ: `feature_name`, `user_commands`).
*   **Files:** `snake_case.js` hoặc `kebab-case.js` (ví dụ: `poll_service.js`, `create-poll-command.js`). Hãy nhất quán.
*   **Variables/Functions:** `camelCase` (ví dụ: `getUserProfile`, `pollResults`).
*   **Classes:** `PascalCase` (ví dụ: `PollService`, `DatabaseConnection`).
*   **Constants:** `UPPER_SNAKE_CASE` (ví dụ: `MAX_POLL_OPTIONS`). **PHẢI** có tên mô tả rõ ràng mục đích của hằng số, tránh "magic numbers" hoặc "magic strings" trong code. Ví dụ, thay vì `if (status === 2) ...`, sử dụng `const PROCESSING_STATUS = 2; if (status === PROCESSING_STATUS) ...`.

## 6. Commit Messages

Sử dụng Conventional Commits (hoặc một format tương tự) để commit message có ý nghĩa và dễ theo dõi:

*   `feat(polls): add ability to create new polls`
*   `fix(streak): correct streak counter logic for timezone changes`
*   `docs(readme): update setup instructions`
*   `test(course): add unit tests for course enrollment service`
*   `refactor(shared): improve logging utility performance`

## 7. Luồng Phát triển Tính năng (Ví dụ)

1.  **Tạo nhánh mới:** `git checkout -b feat/new-voting-system`
2.  **Tạo thư mục:** `src/features/voting/`
3.  **Phát triển code:** Thêm `commands/`, `services/`, etc. vào `src/features/voting/`.
4.  **Viết tests:** Thêm tests vào `tests/features/voting/`.
5.  **Đảm bảo code độc lập:** Kiểm tra không có import chéo giữa các thư mục `features/*`. Nếu cần, đưa code dùng chung vào `shared/` và viết test cho nó.
6.  **Chạy tests:** `npm test` (hoặc lệnh tương ứng).
7.  **Commit code:** Sử dụng commit messages theo quy ước.
8.  **Tạo Pull Request (PR):** Để review code.
9.  **Merge PR:** Sau khi review và tests pass.
10. **Deploy.**

## 8. Nguyên tắc Chung

*   **DRY (Don't Repeat Yourself):** Tránh lặp lại code. Nếu thấy lặp lại, cân nhắc đưa vào `shared/utils` hoặc tạo hàm/class trừu tượng hơn.
*   **SOLID:** Cố gắng tuân thủ các nguyên tắc SOLID.
    *   **Single Responsibility Principle (SRP):** Mỗi class, module, và **hàm** nên chỉ chịu trách nhiệm cho một phần cụ thể của chức năng. Hàm nên nhỏ và làm một việc duy nhất. Nếu một hàm cần comment dài để giải thích nó làm gì, có thể nó đang làm quá nhiều việc và cần được chia nhỏ.
    *   **(Các nguyên tắc SOLID khác):** Open/Closed, Liskov Substitution, Interface Segregation, Dependency Inversion cũng nên được cân nhắc khi thiết kế các module và services phức tạp hơn.
*   **Encapsulation:** Đặc biệt đối với các `services`, che giấu chi tiết triển khai bên trong và chỉ cung cấp một public API rõ ràng để tương tác.
*   **Clean Structure:** Giữ code có liên quan logic với nhau ở gần nhau, cả trong cấu trúc thư mục lẫn trong từng file/module. Điều này giúp dễ tìm kiếm và hiểu code hơn.
*   **Code Review:** Tất cả code mới hoặc thay đổi quan trọng phải được review bởi ít nhất một thành viên khác trong team (nếu có team) hoặc Product Owner.
*   **Tài liệu:**
    *   Viết comment cho những đoạn code phức tạp, những quyết định thiết kế quan trọng, hoặc những giải pháp "workaround". **Comment nên giải thích "tại sao" (why) code được viết như vậy, chứ không chỉ đơn thuần mô tả "cái gì" (what) code đang làm** (vì code nên tự mô tả được điều đó).
    *   README của tính năng (nếu cần) có thể được thêm vào thư mục tính năng.
*   **Ghi chú đầu file (Header Comments):**
    *   Mỗi file mới phải bắt đầu với block comment header gồm các thông tin:
        *   `@file {tên file}` - Tên file.
        *   `@description {Mô tả ngắn về mục đích file}`.
        *   `<AUTHOR> tác giả}`.
        *   `@date {Ngày tạo}`.
    *   Ví dụ:
        ```javascript
        /**
         * @file poll_service.js
         * @description Chứa logic quản lý poll (tạo, vote, kết thúc) cho tính năng Poll.
         * <AUTHOR> Văn A
         * @date 2025-05-09
         */
        ```
    *   **Tự động hóa:** Cấu hình VSCode snippet hoặc pre-commit hook (Husky) để chèn header comment tự động khi tạo file mới.

## 9. Nguyên tắc Đảm bảo Bot Hoạt động Ổn định và Tối ưu

Để bot hoạt động trơn tru, xử lý hiệu quả và dễ dàng bảo trì, các nguyên tắc sau cần được tuân thủ:

1.  **Xử lý lỗi (Robust Error Handling):**
    *   **Try-Catch:** Tất cả các hàm có khả năng phát sinh lỗi (ví dụ: tương tác API, database, xử lý input không đáng tin cậy) **PHẢI** được bao bọc trong khối `try...catch` hoặc trả về kết quả lỗi một cách tường minh (ví dụ: `{ success: false, error: '...' }`).
    *   **Logging Lỗi Chi Tiết:** Khi bắt lỗi, sử dụng `shared/utils/logger.js` để ghi log lỗi với đầy đủ context:
        *   Tên tính năng/module.
        *   Hành động đang thực hiện.
        *   Input liên quan (ẩn thông tin nhạy cảm).
        *   Stack trace của lỗi.
        *   Ví dụ: `logger.error('[StreakService] Failed to increment streak for user ${userId}: ${error.message}', { stack: error.stack });`
    *   **Không để lỗi làm crash bot:** Xử lý lỗi một cách nhẹ nhàng, thông báo cho người dùng nếu cần, và cho phép bot tiếp tục hoạt động với các request khác.
    *   **Thông báo lỗi thân thiện:** Khi có lỗi từ phía người dùng (ví dụ: sai cú pháp lệnh), bot nên trả về thông báo lỗi rõ ràng, dễ hiểu.

2.  **Tối ưu Hiệu năng (Performance Optimization):**
    *   **Thao tác Bất đồng bộ (`async/await`):**
        *   Sử dụng `async/await` cho tất cả các I/O-bound operations (Discord API calls, database queries, đọc/ghi file).
        *   **TUYỆT ĐỐI KHÔNG** sử dụng các hàm đồng bộ (ví dụ: `fs.readFileSync`) trong luồng xử lý request chính vì sẽ block event loop.
    *   **Quản lý Tài nguyên:**
        *   **Discord API Rate Limits (Xử lý Chi tiết):**
            *   **Không Hardcode:** Rate limits của Discord là động và **TUYỆT ĐỐI KHÔNG** được hardcode các giá trị giới hạn hoặc thời gian chờ.
            *   **Đọc và Tuân thủ Headers:** Ứng dụng **PHẢI** phân tích và tuân thủ các HTTP response headers sau để quản lý rate limit:
                *   `X-RateLimit-Limit`: Số lượng request tối đa trong một khoảng thời gian.
                *   `X-RateLimit-Remaining`: Số lượng request còn lại.
                *   `X-RateLimit-Reset-After`: Thời gian (tính bằng giây, có thể là số thập phân) cho đến khi rate limit bucket hiện tại được reset. Đây là header ưu tiên để tính thời gian chờ.
                *   `X-RateLimit-Reset`: Thời điểm (epoch time dạng giây) mà rate limit bucket reset.
                *   `X-RateLimit-Bucket`: Một chuỗi định danh duy nhất cho rate limit bucket đang gặp phải. **Rất quan trọng** để nhóm và quản lý các giới hạn được chia sẻ giữa các route tương tự hoặc các resource khác nhau trên cùng một route.
            *   **Khi gặp HTTP 429 (Too Many Requests):**
                *   **PHẢI** sử dụng giá trị từ header `Retry-After` (nếu có) hoặc trường `retry_after` (float, tính bằng giây) trong JSON response body để xác định chính xác thời gian cần chờ trước khi thử lại request.
                *   Phân tích header `X-RateLimit-Scope` (có thể là `user`, `global`, hoặc `shared`) để hiểu rõ loại giới hạn nào đang bị vi phạm và có chiến lược xử lý phù hợp.
                *   Các header rate limit thông thường (X-RateLimit-Limit, Remaining, etc.) vẫn sẽ được gửi kèm trong response 429.
            *   **Global Rate Limit:**
                *   Bot có thể thực hiện tối đa 50 requests/giây trên toàn cục. Giới hạn này độc lập với các per-route rate limits.
                *   **Lưu ý quan trọng:** Các **Interaction Endpoints** (lệnh slash, buttons, select menus, modals) **KHÔNG** bị ràng buộc bởi Global Rate Limit này.
                *   Nếu bot liên tục bị ban khi khởi động, đó có thể là dấu hiệu của việc vi phạm Global Rate Limit.
            *   **Invalid Request Limit (Nguy cơ bị Cloudflare IP Ban):**
                *   Discord giới hạn 10,000 request không hợp lệ (trả về status 401, 403, hoặc 429) trong mỗi 10 phút cho mỗi IP. Vượt quá giới hạn này sẽ dẫn đến việc IP bị ban tạm thời.
                *   **Cách phòng tránh:**
                    *   **401 (Unauthorized):** Đảm bảo token bot luôn hợp lệ. Ngừng sử dụng token nếu nó không còn giá trị.
                    *   **403 (Forbidden):** Kiểm tra quyền của bot trên server/channel trước khi thực hiện hành động. Không thực hiện các request mà bot không có quyền.
                    *   **429 (Too Many Requests):** Tuân thủ nghiêm ngặt các header rate limit như đã mô tả ở trên.
                    *   **LƯU Ý ĐẶC BIỆT:** Các lỗi 429 có header `X-RateLimit-Scope: shared` **KHÔNG** bị tính vào giới hạn Invalid Request này. Cần phân biệt và xử lý đúng.
                    *   Tránh lặp lại các request đến những tài nguyên đã trả về 404 (Not Found), ví dụ như sử dụng một webhook đã bị xóa.
                *   Đối với các bot lớn, nên cân nhắc việc log và theo dõi tỷ lệ các request không hợp lệ để chủ động tránh bị ban.
            *   **Rate Limit cho Emoji Routes:** Các route quản lý emoji có cơ chế rate limit đặc thù, giới hạn theo từng guild và quota trả về từ API có thể không hoàn toàn chính xác, dễ gặp 429 hơn.
    *   **Xử lý Tác vụ Nặng:** Tránh thực hiện các tác vụ tính toán nặng hoặc lặp qua lượng lớn dữ liệu trực tiếp trong command handlers hoặc event listeners. Nếu cần, xem xét chia nhỏ tác vụ, sử dụng `setTimeout` để giải phóng event loop, hoặc (với các tác vụ rất nặng) nghiên cứu giải pháp worker threads.
        *   **Tối ưu Database:**
            *   Chỉ query những trường cần thiết.
            *   Sử dụng index cho các trường thường xuyên được tìm kiếm.
            *   Cẩn thận với các query có thể trả về lượng lớn dữ liệu. Sử dụng phân trang (pagination) nếu cần.
        *   **Caching:** Cân nhắc caching dữ liệu thường xuyên truy cập và ít thay đổi để giảm tải cho database và API.

3.  **Input Validation và Sanitization:**
    *   **Luôn Validate:** **KHÔNG BAO GIỜ** tin tưởng input từ người dùng hoặc bất kỳ nguồn bên ngoài nào.
    *   Kiểm tra kiểu dữ liệu, định dạng, độ dài, và các ràng buộc khác của input trước khi xử lý.
    *   Sử dụng thư viện validation (ví dụ: Zod, Joi) nếu cần để định nghĩa schema và validate input một cách nhất quán.
    *   Sanitize output nếu hiển thị lại input của người dùng để tránh XSS (mặc dù trong môi trường bot Discord ít rủi ro hơn web).

4.  **Cấu hình và Biến Môi trường:**
    *   **Không Hardcode:** **TUYỆT ĐỐI KHÔNG** hardcode tokens, API keys, connection strings, hoặc các thông tin nhạy cảm khác trực tiếp trong code.
    *   Sử dụng file `.env` (được thêm vào `.gitignore`) để lưu trữ các biến môi trường khi phát triển local.
    *   Truy cập các biến này thông qua `process.env` và quản lý tập trung trong `src/core/config/`.
    *   Khi deploy, thiết lập biến môi trường trên server/hosting platform.

5.  **Logging Toàn diện:**
    *   Ngoài lỗi, log các sự kiện quan trọng khác:
        *   Bot khởi động thành công.
        *   Lệnh được thực thi (tên lệnh, người dùng, guild).
        *   Sự kiện quan trọng được xử lý.
        *   Các quyết định logic quan trọng.
    *   Đảm bảo log **KHÔNG** chứa thông tin nhạy cảm của người dùng (ví dụ: message content đầy đủ nếu không cần thiết, token).
    *   Sử dụng các log level khác nhau (INFO, WARN, ERROR, DEBUG) một cách hợp lý.

Bằng cách tuân thủ các nguyên tắc này, các agent AI (và cả developer con người) có thể đóng góp vào dự án một cách nhất quán, giúp dự án dễ dàng mở rộng, bảo trì và đảm bảo chất lượng khi đưa lên production.

## 10. Nguyên tắc về Trải nghiệm Người dùng (UX) và Tích hợp AI

Để bot không chỉ mạnh mẽ về kỹ thuật mà còn thân thiện, hiệu quả với người dùng và sẵn sàng cho việc tích hợp AI:

1.  **Tối ưu Trải nghiệm Người dùng (UX Principles):**
    *   **Ngôn ngữ và Giao tiếp:**
        *   **Thân thiện và Rõ ràng:** Sử dụng ngôn ngữ tự nhiên, dễ hiểu, thú vị nhưng không quá dài dòng. Tránh biệt ngữ kỹ thuật trừ khi đối tượng người dùng là chuyên gia và điều đó là cần thiết.
        *   **Ngắn gọn và Súc tích:** Đi thẳng vào vấn đề. Thông báo và hướng dẫn nên ngắn gọn nhất có thể mà vẫn đảm bảo đủ thông tin.
        *   **Nhất quán:** Duy trì một giọng điệu (tone-of-voice) nhất quán cho bot trên tất cả các tương tác.
        *   **Hướng dẫn Trực quan:** Cung cấp hướng dẫn rõ ràng cho các lệnh và tính năng, có thể kèm theo ví dụ.
    *   **Thiết kế Luồng Tương tác (Flow Optimization):**
        *   **Đơn giản là Vua:** Thiết kế luồng tương tác sao cho người dùng đạt được mục tiêu với số bước ít nhất và nỗ lực tối thiểu.
        *   **Sử dụng Components của Discord:** Tận dụng tối đa các thành phần tương tác như Buttons, Select Menus, Modals để đơn giản hóa việc nhập liệu và lựa chọn, thay vì yêu cầu người dùng gõ các lệnh dài hoặc nhiều tham số.
        *   **Phản hồi Ngay lập tức:** Bot phải cung cấp phản hồi tức thì cho mỗi hành động của người dùng (ví dụ: emoji xác nhận, tin nhắn "Đang xử lý...", "Lệnh đã được thực hiện."). Điều này giúp người dùng biết bot đã nhận được yêu cầu và đang làm việc.
        *   **Tránh Gây Bối Rối:** Không đặt quá nhiều câu hỏi liên tiếp. Mỗi bước nên tập trung vào một tác vụ hoặc một mẩu thông tin cần thiết. Chia nhỏ các tác vụ phức tạp thành nhiều bước đơn giản nếu cần.
        *   **Dự đoán và Gợi ý:** Nếu có thể, dự đoán nhu cầu tiếp theo của người dùng và đưa ra gợi ý hoặc các hành động tiếp theo khả thi.

2.  **Tích hợp Mô hình AI Agent (AI Agent Integration Principles):**
    *   **Mục đích Rõ ràng:** Luôn xác định rõ ràng giá trị và mục đích của việc tích hợp AI vào một tính năng. AI nên giải quyết một vấn đề cụ thể hoặc nâng cao trải nghiệm người dùng một cách có ý nghĩa.
    *   **Cô lập Logic AI:** Các tương tác với AI models/APIs **PHẢI** được đóng gói trong các `services` riêng biệt. Điều này giúp quản lý, cập nhật và test logic AI dễ dàng hơn.
        *   Ví dụ: `src/features/[feature_name]/services/ai_module_service.js` hoặc `src/shared/services/generic_ai_service.js`.
    *   **Xử lý Bất đồng bộ và Phản hồi Chờ:**
        *   Các lời gọi API đến AI model thường là bất đồng bộ và có thể mất thời gian. Luôn sử dụng `async/await`.
        *   Trong khi chờ phản hồi từ AI, cung cấp thông báo cho người dùng (ví dụ: "Bot đang suy nghĩ...", "AI đang phân tích dữ liệu của bạn...") để họ biết rằng quá trình đang diễn ra.
    *   **Quản lý Lỗi từ AI một cách Thông minh:**
        *   API của AI có thể trả về nhiều loại lỗi (rate limits, lỗi server, nội dung không phù hợp, không có câu trả lời, v.v.).
        *   Implement cơ chế xử lý lỗi chi tiết: log lỗi, thông báo cho người dùng một cách thân thiện ("Xin lỗi, AI hiện tại không thể xử lý yêu cầu này. Vui lòng thử lại sau."), và có thể thử lại với chiến lược backoff nếu phù hợp.
    *   **Bảo mật API Keys:** API keys và các thông tin xác thực khác cho AI services **PHẢI** được lưu trữ an toàn dưới dạng biến môi trường và quản lý thông qua file cấu hình. **TUYỆT ĐỐI KHÔNG** hardcode trong source code.
    *   **Kỹ thuật Prompt (Prompt Engineering):**
        *   Chất lượng của kết quả từ AI phụ thuộc rất nhiều vào prompt. Prompts cần được thiết kế cẩn thận: rõ ràng, cụ thể, cung cấp đủ ngữ cảnh.
        *   Lưu trữ, quản lý và phiên bản hóa các prompts phức tạp một cách có tổ chức.
    *   **Kiểm duyệt Nội dung (Content Moderation):**
        *   Nếu AI agent có khả năng tạo ra nội dung văn bản hoặc hình ảnh, **PHẢI** có cơ chế kiểm duyệt (tự động hoặc dựa trên báo cáo của người dùng) để đảm bảo nội dung tuân thủ quy tắc của Discord và máy chủ, tránh tạo ra nội dung độc hại hoặc không phù hợp.
    *   **Chi phí và Tối ưu Tài nguyên:**
        *   Nếu sử dụng các dịch vụ AI trả phí, cần theo dõi chi phí sử dụng API.
        *   Tối ưu số lượng và độ phức tạp của các lời gọi API đến AI để kiểm soát chi phí và đảm bảo hiệu suất.
    *   **Testing Tích hợp AI:**
        *   Unit test các service gọi AI bằng cách mock lời gọi API và kiểm tra cách service xử lý các phản hồi khác nhau (thành công, lỗi, dữ liệu không mong muốn).
        *   Đối với bản thân AI model, việc test có thể bao gồm các "golden test cases": một bộ các prompts đầu vào và kết quả đầu ra mong đợi để kiểm tra sự ổn định và chất lượng của AI theo thời gian (lưu ý rằng kết quả từ LLMs có thể không hoàn toàn nhất quán).
    *   **Thu thập Phản hồi Người dùng:** Cung cấp cơ chế (ví dụ: một lệnh `/feedback_ai`) để người dùng có thể dễ dàng phản hồi về chất lượng và tính hữu ích của các tính năng được hỗ trợ bởi AI. Dữ liệu này rất quý giá để cải thiện prompts và tinh chỉnh việc tích hợp.

Bằng cách tuân thủ các nguyên tắc này, các agent AI (và cả developer con người) có thể đóng góp vào dự án một cách nhất quán, giúp dự án dễ dàng mở rộng, bảo trì và đảm bảo chất lượng khi đưa lên production.

## 11. Quy tắc về Chất lượng Code, Bảo trì và Bảo mật

Để đảm bảo codebase luôn gọn gàng, nhất quán, dễ bảo trì, an toàn và phù hợp với stack công nghệ:

1.  **Code Style và Linting (Code Style & Linting):**
    *   **Sử dụng Công cụ:** **BẮT BUỘC** sử dụng ESLint để phân tích code tĩnh và Prettier để tự động định dạng code. Điều này đảm bảo tính nhất quán và tuân thủ các chuẩn code.
    *   **Cấu hình Chung:** Dự án PHẢI có file cấu hình ESLint (`.eslintrc.js` hoặc `.eslintrc.json`) và Prettier (`.prettierrc.js` hoặc `.prettierrc.json`) ở thư mục gốc, được chia sẻ và tuân thủ bởi tất cả các thành viên (bao gồm cả AI agents).
    *   **Tự động hóa (Khuyến khích):** Thiết lập pre-commit hooks (ví dụ: sử dụng Husky và lint-staged) để tự động chạy linter và formatter trước mỗi commit. Điều này giúp phát hiện lỗi sớm và giữ code sạch sẽ.
    *   **Không bỏ qua lỗi lint:** Tất cả các lỗi từ ESLint PHẢI được sửa trước khi merge code vào nhánh chính.

2.  **Quản lý Dependencies (Dependency Management):**
    *   **Cam kết Lock Files:** Luôn cam kết file `package-lock.json` (cho npm) hoặc `yarn.lock` (cho Yarn) vào Git repository. Điều này đảm bảo rằng tất cả mọi người và môi trường build sử dụng chính xác cùng một phiên bản của các dependencies.
    *   **Thêm Dependencies Mới một cách Cẩn trọng:**
        *   Trước khi thêm một dependency mới, hãy đánh giá: Mức độ phổ biến, tần suất cập nhật, số lượng issues đang mở, giấy phép sử dụng, và kích thước của thư viện.
        *   Ưu tiên các thư viện được cộng đồng hỗ trợ tốt và bảo trì thường xuyên.
        *   Tránh thêm các dependency không thực sự cần thiết hoặc có chức năng có thể dễ dàng tự implement.
    *   **Cập nhật Dependencies Định kỳ:** Lên kế hoạch xem xét và cập nhật các dependencies (ví dụ: `npm update` hoặc `yarn upgrade`) để nhận các bản vá lỗi, vá bảo mật mới nhất. Luôn test kỹ lưỡng sau khi cập nhật.

3.  **Bảo mật Dependencies (Dependency Security):**
    *   **Kiểm tra Lỗ hổng Thường xuyên:** Chạy `npm audit` (hoặc `yarn audit`) định kỳ và sau mỗi lần cập nhật dependencies để phát hiện các lỗ hổng bảo mật đã biết.
    *   **Khắc phục Lỗ hổng:** Ưu tiên khắc phục các lỗ hổng nghiêm trọng (high/critical severity) càng sớm càng tốt.
    *   **Tích hợp CI/CD (Khuyến khích):** Nếu có thể, tích hợp việc kiểm tra audit vào quy trình CI/CD để tự động phát hiện.

4.  **Refactoring và Loại bỏ Code Chết (Refactoring & Dead Code Removal):**
    *   **Nguyên tắc "Boy Scout Rule":** Luôn cố gắng để lại code sạch sẽ hơn một chút sau mỗi lần bạn làm việc với nó. Nếu thấy một đoạn code có thể cải thiện, hãy dành chút thời gian để refactor.
    *   **Refactor khi Cần thiết:** Chủ động refactor code khi nó trở nên quá phức tạp, khó hiểu, trùng lặp, hoặc hiệu suất kém. Mục tiêu là tăng khả năng đọc, bảo trì và hiệu quả.
    *   **Loại bỏ Code Chết:** Thường xuyên rà soát và loại bỏ code không còn sử dụng (các hàm không được gọi, biến không dùng, file không được import, các đoạn code đã được comment out trong thời gian dài). Điều này giúp codebase gọn gàng và giảm thiểu sự nhầm lẫn.

5.  **Tài liệu Code chi tiết (JSDoc):**
    *   **Yêu cầu cho Public API:** **BẮT BUỘC** sử dụng JSDoc cho tất cả các hàm/phương thức/classes được export từ các modules, đặc biệt là trong `services/`, `shared/utils/`, `core/` và các file `index.js` của từng tính năng.
    *   **Nội dung JSDoc Tối thiểu:**
        *   Mô tả ngắn gọn (`@description`) về chức năng, **tại sao** nó tồn tại nếu không rõ ràng.
        *   Mô tả cho từng tham số (`@param {type} name - description`).
        *   Mô tả giá trị trả về (`@returns {type} - description`).
        *   Gắn tag `@async` nếu là hàm bất đồng bộ.
        *   Ví dụ sử dụng (`@example`) nếu logic phức tạp hoặc có nhiều cách sử dụng.
    *   **Mục đích:** Giúp người đọc (và AI) hiểu cách sử dụng một module hoặc hàm mà không cần phải đọc toàn bộ mã nguồn của nó.

6.  **Cập nhật Tài liệu Dự án Chính (Main Project README):**
    *   File `README.md` ở thư mục gốc của dự án **PHẢI** được duy trì và cập nhật thường xuyên với các thông tin sau:
        *   Mô tả tổng quan về dự án và mục tiêu của bot.
        *   Hướng dẫn cài đặt chi tiết (yêu cầu phiên bản Node.js, cách cài dependencies).
        *   Cách cấu hình môi trường (các biến môi trường cần thiết và cách tạo file `.env`).
        *   Các lệnh NPM/Yarn scripts chính (ví dụ: `npm start`, `npm run dev`, `npm test`, `npm run lint`).
        *   Tổng quan ngắn gọn về cấu trúc thư mục chính.

7. **Module System Nhất quán (JavaScript Modules):**
    *   Dự án này hiện tại đang sử dụng CommonJS (`require()` và `module.exports`). **PHẢI** duy trì sự nhất quán này trong toàn bộ codebase.
    *   Nếu có quyết định chuyển sang ES Modules (`import`/`export`), việc chuyển đổi phải được thực hiện đồng bộ cho toàn bộ dự án và được ghi lại như một thay đổi lớn. **Lưu ý: ES Modules được khuyến nghị cho các dự án mới hoặc khi có đợt refactor lớn để tận dụng các tính năng mới và tối ưu của JavaScript.**

Bằng cách tuân thủ các nguyên tắc này, các agent AI (và cả developer con người) có thể đóng góp vào dự án một cách nhất quán, giúp dự án dễ dàng mở rộng, bảo trì và đảm bảo chất lượng khi đưa lên production.

## 12. Quy trình Lập kế hoạch và Phát triển Tính năng (Feature Planning & Development Workflow)

Để đảm bảo các tính năng được phát triển đúng hướng, yêu cầu được hiểu rõ và công việc được chia nhỏ hợp lý, quy trình sau PHẢI được tuân thủ cho mỗi tính năng mới có quy mô đáng kể:

1.  **Tạo Tài liệu Kế hoạch Tính năng (Feature Planning Document - FPD):**
    *   **Khi nào:** Trước khi bắt đầu viết bất kỳ code nào cho một tính năng mới hoặc một thay đổi lớn.
    *   **Định dạng:** File Markdown (`.md`).
    *   **Vị trí:** Đặt tại `docs/planning/[feature-name]/[feature-name]-FPD.md` (ví dụ: `docs/planning/polls/polls-FPD.md`) hoặc trong thư mục tính năng `src/features/[feature-name]/PLANNING.md`. Cần thống nhất cách đặt tên và vị trí.

2.  **Nội dung Bắt buộc của FPD:**

    *   **A. Tổng quan về Tính năng (Feature Overview):**
        *   **Tên Tính năng:** (Ví dụ: Quản lý Cuộc bình chọn - Polls Management)
        *   **Người yêu cầu/Product Owner:** (Tên của bạn)
        *   **Ngày yêu cầu:**
        *   **Mô tả ngắn gọn:** Tính năng này giải quyết vấn đề gì? Mục tiêu chính là gì?
        *   **Giá trị mang lại:** Lợi ích cho người dùng và/hoặc dự án.

    *   **B. Thu thập và Làm rõ Yêu cầu (Requirement Elicitation & Clarification):**
        *   **1. Yêu cầu Ban đầu từ Product Owner:** Chép lại hoặc tóm tắt các yêu cầu ban đầu do bạn cung cấp.
        *   **2. Câu hỏi Làm rõ Yêu cầu (Agent to Product Owner):**
            *   Đây là phần AI agent (hoặc developer) đặt câu hỏi để làm sáng tỏ mọi khía cạnh của yêu cầu.
            *   Mục tiêu: Đảm bảo hiểu biết đầy đủ và chính xác, tránh các giả định sai lầm.
            *   Ví dụ: "Đối với tính năng X, khi điều kiện Y xảy ra, bot nên thực hiện hành động A hay B?", "Có giới hạn nào về số lượng Z không?", "Nếu người dùng nhập sai định dạng cho W, thông báo lỗi cụ thể nên là gì?"
        *   **3. Các Giả định đã được Chấp nhận (Accepted Assumptions):** Sau khi thảo luận, liệt kê các giả định được cả hai bên đồng ý để tiến hành công việc nếu vẫn còn điểm chưa hoàn toàn rõ ràng.

    *   **C. Phạm vi Tính năng (Scope Definition):**
        *   **Trong Phạm vi (In Scope):** Liệt kê chi tiết các chức năng, user stories cụ thể sẽ được triển khai trong lần phát triển này.
        *   **Ngoài Phạm vi (Out of Scope):** Liệt kê những gì sẽ **KHÔNG** được thực hiện trong lần này (để quản lý kỳ vọng và tránh "scope creep"). Những mục này có thể được xem xét cho các giai đoạn sau.

    *   **D. Thiết kế Kỹ thuật và Kế hoạch Triển khai (Technical Design & Implementation Plan):**
        *   **1. User Stories / Use Cases (Chi tiết):** Mô tả từng kịch bản người dùng chính, bao gồm cả các trường hợp thành công và thất bại. Tham chiếu đến Mermaid diagrams trong `README.md` của tính năng (nếu có).
        *   **2. Luồng Dữ liệu (Data Flow):** (Tùy chọn, có thể dùng Mermaid) Mô tả cách dữ liệu di chuyển và được xử lý.
        *   **3. Thay đổi Database (Nếu có):** Mô tả schema mới hoặc các thay đổi đối với schema hiện tại, các migrations cần thiết.
        *   **4. Tương tác API Bên ngoài (Nếu có):** Liệt kê các API sẽ sử dụng, endpoints, định dạng request/response dự kiến.
        *   **5. Chia nhỏ Công việc (Task Breakdown):**
            *   Phân rã tính năng thành các task nhỏ, có thể quản lý và thực hiện được.
            *   Với mỗi task:
                *   Mô tả ngắn gọn (ví dụ: "Tạo command `create_poll`").
                *   Các file/module chính sẽ tạo mới hoặc chỉnh sửa.
                *   Ước tính nỗ lực (ví dụ: nhỏ, vừa, lớn hoặc theo giờ - tùy chọn).
                *   Các task phụ thuộc (dependencies).

    *   **E. Chiến lược Kiểm thử (Testing Strategy):**
        *   Mô tả cách tính năng sẽ được kiểm thử.
        *   Liệt kê các kịch bản test chính cho Unit Test và Integration Test.
        *   Đề cập đến việc chuẩn bị dữ liệu test.

    *   **F. Rủi ro và Vấn đề Mở (Risks & Open Issues):**
        *   Liệt kê các rủi ro tiềm ẩn (kỹ thuật, thời gian, dependencies) và kế hoạch giảm thiểu (nếu có).
        *   Ghi lại các câu hỏi hoặc vấn đề kỹ thuật chưa được giải quyết cần nghiên cứu thêm.

3.  **Quy trình Làm việc với FPD:**
    *   **Bước 1: Tạo FPD (AI/Developer):** Dựa trên yêu cầu ban đầu, AI agent (hoặc developer) soạn thảo bản FPD đầu tiên, đặc biệt chú trọng phần "Câu hỏi Làm rõ Yêu cầu".
    *   **Bước 2: Review và Hoàn thiện (Product Owner & AI/Developer):** Bạn (Product Owner) xem xét FPD, trả lời các câu hỏi, cung cấp thêm chi tiết. Quá trình này có thể lặp lại cho đến khi FPD được cả hai bên thống nhất và bạn cảm thấy yêu cầu của mình đã được nắm bắt đầy đủ.
    *   **Bước 3: "Đóng băng" Yêu cầu (Sau khi thống nhất):** Sau khi FPD được thống nhất, các yêu cầu chính trong đó được coi là "đóng băng" cho lần phát triển này. Mọi thay đổi lớn sau đó cần được thảo luận và có thể yêu cầu cập nhật FPD.
    *   **Bước 4: Phát triển dựa trên FPD:** FPD là tài liệu định hướng chính cho quá trình viết code và unit test.
    *   **Bước 5: Cập nhật FPD (Nếu có thay đổi lớn):** Nếu có những thay đổi không thể tránh khỏi hoặc những khám phá mới trong quá trình phát triển làm ảnh hưởng đáng kể đến phạm vi hoặc thiết kế, FPD PHẢI được cập nhật để phản ánh những thay đổi đó.

Việc tuân thủ quy trình này giúp đảm bảo sự rõ ràng, nhất quán và hiệu quả trong việc phát triển từng tính năng, giảm thiểu rủi ro làm sai yêu cầu hoặc bỏ sót nhiệm vụ.

Bằng cách tuân thủ các nguyên tắc này, các agent AI (và cả developer con người) có thể đóng góp vào dự án một cách nhất quán, giúp dự án dễ dàng mở rộng, bảo trì và đảm bảo chất lượng khi đưa lên production.

## 13. Quy trình Làm việc với Git (Git Workflow - Áp dụng Gitflow)

Để quản lý source code một cách hiệu quả, nhất quán và hỗ trợ quy trình phát triển/release chuyên nghiệp, dự án áp dụng Gitflow Workflow với các quy tắc sau:

### 1. Các Nhánh Chính (Main Branches)

*   **`main` (hoặc `master`)**
    *   Chứa code đã sẵn sàng cho môi trường Production (production-ready).
    *   **TUYỆT ĐỐI KHÔNG** commit trực tiếp vào nhánh `main`.
    *   Chỉ chấp nhận merge từ các nhánh sau:
        *   `hotfix/*`
        *   `release/*`
    *   Mỗi lần merge vào `main` từ một nhánh `release/*` hoặc `hotfix/*` **PHẢI** được gắn tag với một số phiên bản mới theo Semantic Versioning (ví dụ: `v1.0.0`, `v1.0.1`).

*   **`develop`**
    *   Là nhánh phát triển chính, tích hợp các thay đổi đã hoàn thành từ các nhánh `feature`.
    *   Luôn phản ánh trạng thái mới nhất của code đang được phát triển cho lần release tiếp theo.
    *   Là nhánh nguồn để tạo ra các nhánh `feature/*` và `release/*`.
    *   **TUYỆT ĐỐI KHÔNG** commit trực tiếp vào nhánh `develop`. Thay vào đó, merge từ các nhánh `feature/*`, `release/*` (sau khi release), hoặc `hotfix/*` (sau khi hotfix).

### 2. Các Nhánh Hỗ trợ (Supporting Branches)

*   **`feature/*`**
    *   **Phân nhánh từ:** `develop`
    *   **Merge trở lại vào:** `develop`
    *   **Quy ước đặt tên:** `feature/[issue-id]-ten-tinh-nang-mo-ta` (ví dụ: `feature/45-user-profile-api`, `feature/auth-discord-integration`). Nếu không có issue ID, có thể dùng `feature/ten-tinh-nang-mo-ta`.
    *   Mỗi tính năng mới PHẢI được phát triển trên một nhánh `feature` riêng biệt.
    *   Trước khi tạo Pull Request (PR) để merge vào `develop`, nhánh `feature` PHẢI được cập nhật (rebase hoặc merge) với những thay đổi mới nhất từ `develop`.
    *   Nhánh `feature` NÊN được xóa sau khi đã merge thành công vào `develop`.

*   **`release/*`**
    *   **Phân nhánh từ:** `develop` (khi `develop` đã có đủ các tính năng sẵn sàng cho một bản release mới).
    *   **Merge trở lại vào:**
        *   `main` (để release lên production)
        *   `develop` (để đảm bảo các thay đổi trong quá trình chuẩn bị release cũng có mặt trên `develop`)
    *   **Quy ước đặt tên:** `release/vX.Y.Z` (ví dụ: `release/v1.2.0`).
    *   Nhánh `release` chỉ dành cho các công việc liên quan trực tiếp đến việc chuẩn bị release: sửa các bug nhỏ cuối cùng, cập nhật tài liệu, chuẩn bị các file cấu hình cho release.
    *   **KHÔNG** phát triển tính năng mới trên nhánh `release`.
    *   Nhánh `release` NÊN được xóa sau khi đã merge thành công vào `main` và `develop`.

*   **`hotfix/*`**
    *   **Phân nhánh từ:** `main` (khi có lỗi khẩn cấp trên môi trường Production cần được sửa ngay).
    *   **Merge trở lại vào:**
        *   `main` (để vá lỗi trên production)
        *   `develop` (để đảm bảo lỗi cũng được sửa trên nhánh phát triển chính)
    *   **Quy ước đặt tên:** `hotfix/vX.Y.Z` (ví dụ: `hotfix/v1.2.1`) hoặc `hotfix/[issue-id]-mo-ta-loi`.
    *   Chỉ dành cho việc sửa các lỗi nghiêm trọng trên Production.
    *   Nhánh `hotfix` NÊN được xóa sau khi đã merge thành công vào `main` và `develop`.

### 3. Commit Messages

*   Tuân thủ quy ước đã nêu ở Mục 6 (Conventional Commits): `type(scope): description`.
*   Các `type` chính bao gồm: `feat`, `fix`, `docs`, `style`, `refactor`, `test`, `chore`, `perf`, `ci`, `build`.
*   Commit message phải rõ ràng, mô tả đúng thay đổi.

### 4. Quản lý Phiên bản (Semantic Versioning - SemVer)

*   Dự án sử dụng Semantic Versioning (X.Y.Z - MAJOR.MINOR.PATCH).
    *   **MAJOR (X):** Tăng khi có những thay đổi API không tương thích ngược (breaking changes).
    *   **MINOR (Y):** Tăng khi thêm tính năng mới mà vẫn đảm bảo tương thích ngược.
    *   **PATCH (Z):** Tăng khi sửa lỗi mà vẫn đảm bảo tương thích ngược.
*   Phiên bản được quản lý trong `package.json` và được sử dụng để tag các release trên nhánh `main`.

### 5. Quy tắc Pull Request (PR)

*   Tất cả các thay đổi code vào nhánh `develop` và `main` **PHẢI** thông qua Pull Request.
*   Mỗi PR cần ít nhất **một (1) lượt approve** từ một thành viên khác (nếu có team) hoặc từ Product Owner (là bạn) trước khi được merge.
*   Tất cả các kiểm tra tự động (CI checks - ví dụ: tests, linting) **PHẢI** pass.
*   **KHÔNG** được phép commit trực tiếp lên các nhánh được bảo vệ (`main`, `develop`).
*   Nhánh nguồn của PR (ví dụ: `feature/*`) **PHẢI** được cập nhật với nhánh đích (ví dụ: `develop`) trước khi merge để giải quyết conflicts (nếu có).
*   Xóa nhánh nguồn sau khi PR đã được merge thành công.
*   Mô tả PR phải rõ ràng: giải thích mục đích của PR, các thay đổi chính, và link đến FPD hoặc issue (nếu có).

### 6. Bảo vệ Nhánh (Branch Protection Rules)

Áp dụng các quy tắc bảo vệ cho nhánh `main` và `develop` trên Git provider (GitHub, GitLab, etc.):

*   Yêu cầu Pull Request review trước khi merge.
*   Yêu cầu tất cả status checks (CI) phải pass trước khi merge.
*   Yêu cầu nhánh phải được cập nhật với nhánh đích trước khi merge.
*   Hạn chế force push (lý tưởng là cấm hoàn toàn).
*   Hạn chế việc xóa nhánh (lý tưởng là cấm hoàn toàn).
*   Áp dụng các quy tắc này cho cả administrators.

### 7. Quy trình Release

1.  **Tạo nhánh `release/*`:** Khi `develop` đã sẵn sàng cho một release mới, tạo nhánh `release/vX.Y.Z` từ `develop`.
2.  **Hoàn tất Release:** Trên nhánh `release`, thực hiện các công việc cuối cùng: bump version trong `package.json` và các file liên quan, cập nhật `CHANGELOG.md`, sửa các bug nhỏ cuối cùng.
3.  **Tạo PR vào `main`:** Tạo một PR từ nhánh `release/*` vào `main`.
4.  **Merge vào `main`:** Sau khi PR được review và approve, merge nhánh `release/*` vào `main`.
5.  **Tag Release trên `main`:** Ngay sau khi merge vào `main`, tạo một Git tag với tên phiên bản (ví dụ: `git tag -a vX.Y.Z -m "Release version X.Y.Z"`). Push tag lên remote.
6.  **Merge `release/*` trở lại `develop`:** Merge nhánh `release/*` (hoặc `main` vừa được cập nhật) trở lại vào `develop` để đảm bảo `develop` cũng có các thay đổi từ quá trình release.
7.  **Xóa nhánh `release/*`:** Sau khi đã hoàn tất.

### 8. Quy trình Hotfix

1.  **Tạo nhánh `hotfix/*`:** Khi có lỗi khẩn cấp trên `main`, tạo nhánh `hotfix/*` (ví dụ: `hotfix/vX.Y.Z+1` hoặc `hotfix/fix-critical-login-bug`) từ commit mới nhất trên `main` (hoặc từ tag của phiên bản bị lỗi).
2.  **Sửa lỗi:** Thực hiện việc sửa lỗi trên nhánh `hotfix`.
3.  **Bump Version (Patch):** Cập nhật PATCH version trong `package.json`.
4.  **Tạo PR vào `main`:** Tạo một PR từ nhánh `hotfix/*` vào `main`.
5.  **Merge vào `main`:** Sau khi PR được review và approve, merge nhánh `hotfix/*` vào `main`.
6.  **Tag Release trên `main`:** Tạo một Git tag mới cho phiên bản hotfix (ví dụ: `v1.2.1`). Push tag lên remote.
7.  **Merge `hotfix/*` trở lại `develop`:** Merge nhánh `hotfix/*` (hoặc `main` vừa được cập nhật) trở lại vào `develop`.
8.  **Xóa nhánh `hotfix/*`:** Sau khi đã hoàn tất.

---
*Chỉnh sửa mục 5: Quy ước Đặt tên*

*   **Thư mục:** `snake_case` (ví dụ: `feature_name`, `user_commands`).
*   **Files:** `snake_case.js` hoặc `kebab-case.js` (ví dụ: `poll_service.js`, `create-poll-command.js`). Hãy nhất quán.
*   **Variables/Functions:** `camelCase` (ví dụ: `getUserProfile`, `pollResults`).
*   **Classes:** `PascalCase` (ví dụ: `PollService`, `DatabaseConnection`).
*   **Constants:** `UPPER_SNAKE_CASE` (ví dụ: `MAX_POLL_OPTIONS`). **PHẢI** có tên mô tả rõ ràng mục đích của hằng số, tránh "magic numbers" hoặc "magic strings" trong code. Ví dụ, thay vì `if (status === 2) ...`, sử dụng `const PROCESSING_STATUS = 2; if (status === PROCESSING_STATUS) ...`.

---
*Chỉnh sửa mục 8: Nguyên tắc Chung*

*   **DRY (Don't Repeat Yourself):** Tránh lặp lại code. Nếu thấy lặp lại, cân nhắc đưa vào `shared/utils` hoặc tạo hàm/class trừu tượng hơn.
*   **SOLID:** Cố gắng tuân thủ các nguyên tắc SOLID.
    *   **Single Responsibility Principle (SRP):** Mỗi class, module, và **hàm** nên chỉ chịu trách nhiệm cho một phần cụ thể của chức năng. Hàm nên nhỏ và làm một việc duy nhất. Nếu một hàm cần comment dài để giải thích nó làm gì, có thể nó đang làm quá nhiều việc và cần được chia nhỏ.
    *   **(Các nguyên tắc SOLID khác):** Open/Closed, Liskov Substitution, Interface Segregation, Dependency Inversion cũng nên được cân nhắc khi thiết kế các module và services phức tạp hơn.
*   **Encapsulation:** Đặc biệt đối với các `services`, che giấu chi tiết triển khai bên trong và chỉ cung cấp một public API rõ ràng để tương tác.
*   **Clean Structure:** Giữ code có liên quan logic với nhau ở gần nhau, cả trong cấu trúc thư mục lẫn trong từng file/module. Điều này giúp dễ tìm kiếm và hiểu code hơn.
*   **Code Review:** Tất cả code mới hoặc thay đổi quan trọng phải được review bởi ít nhất một thành viên khác trong team (nếu có team) hoặc Product Owner.
*   **Tài liệu:**
    *   Viết comment cho những đoạn code phức tạp, những quyết định thiết kế quan trọng, hoặc những giải pháp "workaround". **Comment nên giải thích "tại sao" (why) code được viết như vậy, chứ không chỉ đơn thuần mô tả "cái gì" (what) code đang làm** (vì code nên tự mô tả được điều đó).
    *   README của tính năng (nếu cần) có thể được thêm vào thư mục tính năng.
*   **Ghi chú đầu file (Header Comments):** (Đã có)

## 14. Các Mẫu Thiết kế và Nguyên tắc cho Bot Discord

Ngoài các nguyên tắc chung, một số mẫu thiết kế và nguyên tắc cụ thể sau đây rất hữu ích khi phát triển bot Discord:

1.  **Command Pattern:**
    *   Xem xét việc đóng gói mỗi lệnh như một đối tượng riêng biệt. Điều này cho phép quản lý lệnh linh hoạt hơn (ví dụ: dễ dàng thêm, xóa, hoặc sửa đổi lệnh), hỗ trợ undo/redo (nếu cần), và giúp việc đăng ký lệnh rõ ràng hơn.
    *   Nhiều thư viện command framework cho discord.js (bao gồm cả command handler tích hợp sẵn) đều dựa trên hoặc khuyến khích pattern này.

2.  **Observer Pattern (Events):**
    *   Discord API và các thư viện client (như discord.js) hoạt động mạnh mẽ dựa trên events. Tận dụng cơ chế này để tách biệt logic xử lý sự kiện khỏi các phần khác của bot.
    *   Sử dụng `client.on('eventName', handlerFunction)` một cách hiệu quả.

3.  **Singleton Pattern:**
    *   Đối với các tài nguyên cần được chia sẻ và chỉ nên có một instance duy nhất trong toàn bộ ứng dụng (ví dụ: kết nối database, một service cấu hình trung tâm, client của một API bên thứ ba), Singleton pattern có thể hữu ích để đảm bảo tính nhất quán và tránh lãng phí tài nguyên.

4.  **Factory Pattern:**
    *   Khi cần tạo các đối tượng phức tạp hoặc có nhiều biến thể (ví dụ: tạo Discord Embeds với các cấu trúc khác nhau, tạo các loại message response khác nhau), Factory pattern có thể giúp đơn giản hóa việc khởi tạo và làm cho code dễ quản lý hơn.

5.  **Sử dụng Command Framework (Khuyến khích):**
    *   Thay vì tự xây dựng toàn bộ logic parsing lệnh, validation, và routing, hãy cân nhắc sử dụng các command framework có sẵn của discord.js hoặc các thư viện cộng đồng. Chúng thường cung cấp các tính năng mạnh mẽ như xử lý alias, cooldown, permissions, sub-commands, giúp bạn tập trung vào logic của lệnh.

## 15. Các Cạm bẫy Thường gặp và Lưu ý khi làm việc với Discord API

Làm việc với Discord API và các thư viện client có những đặc thù riêng. Lưu ý các điểm sau để tránh lỗi phổ biến:

1.  **Rate Limiting:**
    *   Discord API có cơ chế rate limit nghiêm ngặt để tránh lạm dụng. Luôn ý thức về điều này.
    *   Các thư viện client (như discord.js) thường có cơ chế xử lý rate limit nội bộ, nhưng bạn vẫn cần cẩn thận khi thực hiện số lượng lớn API call trong thời gian ngắn (ví dụ: gửi tin nhắn hàng loạt, cập nhật roles cho nhiều user).
    *   Nếu tự gọi API trực tiếp, bạn PHẢI implement xử lý rate limit (kiểm tra headers, retry với backoff).

2.  **Gateway Intents:**
    *   Kể từ Discord API v6, bot cần khai báo rõ ràng các "Intents" (ý định) mà nó muốn nhận từ Gateway (ví dụ: `GUILD_MESSAGES`, `GUILD_MEMBERS`).
    *   **Không khai báo đủ Intents** sẽ khiến bot không nhận được các event tương ứng (ví dụ: không có `GUILD_MEMBERS` intent thì event `guildMemberAdd` sẽ không được gửi).
    *   **Khai báo Intents không cần thiết**, đặc biệt là các Privileged Intents, có thể khiến bot của bạn cần quá trình xác minh (verification) nếu đạt đến một số lượng server nhất định.

3.  **Privileged Gateway Intents:**
    *   Các intents như `GUILD_PRESENCES`, `GUILD_MEMBERS`, và `MESSAGE_CONTENT` (từ API v10) là "Privileged Intents".
    *   Để sử dụng chúng, bạn cần bật chúng trong Developer Portal của bot.
    *   Nếu bot của bạn ở trên 100 server trở lên, việc sử dụng Privileged Intents sẽ yêu cầu bot phải qua quy trình verification từ Discord. Hãy chỉ yêu cầu những intent bạn thực sự cần.

4.  **Xử lý Bất đồng bộ (Async Operations):**
    *   Hầu hết các tương tác với Discord API đều là bất đồng bộ. Luôn sử dụng `async/await` hoặc Promises một cách chính xác để tránh race conditions hoặc code chạy không theo thứ tự mong muốn.
    *   Đảm bảo `await` các Promises trả về từ các hàm API của discord.js.

5.  **Data Serialization/Deserialization:**
    *   Khi tương tác với API (cả Discord API lẫn các API bên ngoài khác), hãy chú ý đến định dạng dữ liệu (thường là JSON) và cách dữ liệu được serialize/deserialize.

6.  **Thay đổi và Phiên bản API/Thư viện:**
    *   Discord API và các thư viện client như discord.js liên tục được cập nhật. Theo dõi các thông báo về breaking changes.
    *   Khi nâng cấp phiên bản thư viện, hãy đọc kỹ changelog và kiểm tra lại code để đảm bảo tương thích. Tránh sử dụng các hàm/thuộc tính đã bị deprecated.

7.  **Các Trường hợp Biên (Edge Cases):**
    *   **Guild lớn:** Xử lý các tác vụ trên guild có hàng chục ngàn thành viên hoặc kênh có thể cần tối ưu đặc biệt (ví dụ: tránh fetch tất cả members cùng lúc).
    *   **Lỗi API không mong muốn:** Chuẩn bị cho các lỗi lạ hoặc outages từ phía Discord.
    *   **Nhiều ngôn ngữ/locale:** Nếu bot hỗ trợ đa ngôn ngữ, cần có cơ chế quản lý và hiển thị phù hợp.
    *   **Concurrency:** Cẩn thận với các vấn đề về đồng thời khi nhiều người dùng tương tác với cùng một tài nguyên hoặc chức năng của bot.

8.  **Debugging Chiến lược cho Bot Discord:**
    *   **Logging chi tiết:** (Đã đề cập ở Rule 9)
    *   **Sử dụng Debugger:** Các IDE hiện đại đều hỗ trợ debug Node.js.
    *   **Discord Developer Portal:** Kiểm tra Audit Log của server, thông tin Gateway, và các thống kê của bot.
    *   **Discord Developer Tools (trong client):** (Ít dùng trực tiếp cho bot, nhưng hữu ích khi tìm hiểu cách client tương tác).

## 16. Bảo mật cho Bot Discord

Bảo mật là yếu tố then chốt. Ngoài các nguyên tắc bảo mật chung (Rule 11), cần chú trọng các điểm sau cho bot Discord:

1.  **Bảo vệ Bot Token:** (Nhấn mạnh lại Rule 9.4)
    *   Bot Token là chìa khóa truy cập vào tài khoản bot của bạn. **TUYỆT ĐỐI KHÔNG** hardcode token trong code, không commit token lên Git, không chia sẻ token công khai.
    *   Sử dụng biến môi trường để lưu trữ token.

2.  **Kiểm soát Quyền của Bot (Bot Permissions):**
    *   Khi mời bot vào server, chỉ cấp cho bot những quyền (permissions) thực sự cần thiết cho hoạt động của nó. Tránh cấp quyền Administrator nếu không bắt buộc.
    *   Định kỳ xem xét lại các quyền của bot.

3.  **Input Validation cho Commands:** (Nhấn mạnh lại Rule 9.3)
    *   Xác thực và làm sạch (sanitize) tất cả các input từ người dùng qua các lệnh.
    *   Đề phòng các trường hợp người dùng cố tình nhập liệu độc hại để gây lỗi hoặc khai thác lỗ hổng (ví dụ: cố gắng inject code vào các lệnh eval - mặc dù `eval` nên tránh).
    *   **Chủ động Tránh Các Request API Không Hợp Lệ:** Tích cực làm việc để ngăn chặn các phản hồi 401 (vấn đề token) và 403 (vấn đề quyền) từ Discord API. Việc này không chỉ giúp bot hoạt động đúng mà còn tránh bị giới hạn bởi Invalid Request Limit và nguy cơ bị IP ban.

4.  **Ngăn chặn Lạm dụng và Spam Command:**
    *   Implement cơ chế cooldown cho các lệnh để tránh người dùng spam lệnh.
    *   Xem xét giới hạn số lần thực hiện một số lệnh nhất định trong một khoảng thời gian.

5.  **Xử lý Lỗi và Thông tin Nhạy cảm:**
    *   Không hiển thị thông tin lỗi chi tiết (stack traces, cấu hình nội bộ) trực tiếp cho người dùng. Thay vào đó, log lỗi chi tiết và hiển thị một thông báo lỗi thân thiện, chung chung.
    *   Đảm bảo log không ghi lại thông tin quá nhạy cảm của người dùng nếu không cần thiết.

6.  **SQL Injection (Nếu sử dụng Database):**
    *   Nếu bot tương tác với SQL database, luôn sử dụng parameterized queries hoặc ORM để tránh lỗ hổng SQL Injection.

7.  **Denial of Service (DoS/DDoS):**
    *   Mặc dù khó bảo vệ hoàn toàn ở tầng ứng dụng bot, việc có rate limiting cho command, xử lý lỗi tốt, và tối ưu hiệu năng có thể giúp giảm thiểu tác động.
    *   Đối với các cuộc tấn công quy mô lớn, cần giải pháp ở tầng hạ tầng mạng.

8.  **Unauthorized Access vào Chức năng Admin:**
    *   Nếu bot có các lệnh quản trị đặc biệt, đảm bảo chúng được bảo vệ chặt chẽ bằng cách kiểm tra vai trò (roles) hoặc ID người dùng cụ thể.

9.  **Mã hóa Dữ liệu Nhạy cảm:**
    *   Nếu bot lưu trữ dữ liệu nhạy cảm của người dùng trong database (ngoài các thông tin Discord cung cấp), cân nhắc việc mã hóa các dữ liệu này.

## 17. Triển khai và Vận hành Bot (Deployment & Operations)

**Lưu ý quan trọng cho AI Agent và Developer:** Thông tin trong mục này, đặc biệt các chi tiết về Dokploy, được cung cấp chủ yếu để bạn có **bối cảnh đầy đủ về môi trường và quy trình triển khai thực tế của bot.** Việc hiểu rõ cách bot được build, cấu hình và vận hành trên Dokploy sẽ giúp bạn:
*   Viết code tương thích và tối ưu cho môi trường đó.
*   Tránh đưa ra các quyết định thiết kế hoặc thêm các thành phần có thể gây xung đột, phức tạp hóa hoặc cản trở quá trình triển khai và vận hành do Product Owner/Quản trị viên thực hiện.
*   Ví dụ: Hiểu về cách quản lý biến môi trường, build Docker image, hoặc các giới hạn tài nguyên trên Dokploy có thể ảnh hưởng đến cách bạn cấu trúc một số phần của ứng dụng hoặc xử lý các phụ thuộc.
**Nhiệm vụ của bạn KHÔNG BAO GỒM việc trực tiếp thực hiện các thao tác deploy này.**

Sau khi phát triển, việc triển khai và vận hành bot một cách ổn định là rất quan trọng. Nếu sử dụng một nền tảng như Dokploy, nhiều khía cạnh vận hành sẽ được đơn giản hóa. Dưới đây là các nguyên tắc chung và các lưu ý cụ thể khi dùng Dokploy:

1.  **Process Manager và Tự động Khởi động lại:**
    *   **Khi sử dụng Dokploy:** Việc quản lý tiến trình (process management) và tự động khởi động lại bot khi crash thường được xử lý bởi **Docker Swarm** mà Dokploy sử dụng. Bạn sẽ cấu hình **Restart Policy** trong phần `Advanced -> Swarm Settings` của ứng dụng trong Dokploy (ví dụ: `Condition: on-failure`, `Max Attempts: 3`). Điều này thay thế việc cài đặt PM2 hoặc systemd thủ công trên server cho tiến trình bot.
    *   Dokploy cũng hỗ trợ **Health Checks** (trong `Swarm Settings`) để Docker Swarm kiểm tra tình trạng của bot và khởi động lại nếu cần. Đối với bot Discord không có HTTP endpoint, một healthcheck dựa trên việc tiến trình còn sống (`CMD-SHELL docker ps -q --filter name=YOUR_SERVICE_NAME_IN_SWARM | grep -q .` hoặc tương tự) hoặc một healthcheck mặc định của Docker thường là đủ. Nếu bot có thể cung cấp một lệnh CLI đơn giản để kiểm tra trạng thái, nó có thể được tích hợp.

2.  **Nền tảng và Chiến lược Triển khai với Dokploy:**
    *   Dokploy được cài đặt trên VPS của bạn, và bạn sẽ triển khai bot Discord lên Dokploy.
    *   **Chiến lược Build & Deploy được khuyến nghị cho Production (theo Dokploy docs):**
        *   **Bước 1: Tạo Dockerfile cho Bot:** Xây dựng một `Dockerfile` tối ưu cho Node.js Discord bot của bạn. Tham khảo các ví dụ multi-stage build để giữ image nhẹ và an toàn (như trong tài liệu "Going Production" của Dokploy).
        *   **Bước 2: CI/CD Pipeline (ví dụ: GitHub Actions):** Thiết lập một CI/CD pipeline để tự động:
            *   Build Docker image từ `Dockerfile` mỗi khi có thay đổi trên nhánh chính (hoặc release branch).
            *   Chạy tests.
            *   Push Docker image đã build (gắn tag phiên bản) lên một Docker Registry (ví dụ: Docker Hub, GitHub Container Registry - GHCR, GitLab Registry).
        *   **Bước 3: Triển khai trên Dokploy:**
            *   Trong Dokploy, tạo một Application mới.
            *   Chọn `Source Type` là **`Docker Image`**.
            *   Điền tên image từ Docker Registry (ví dụ: `yourusername/discord-bot:latest` hoặc `yourusername/discord-bot:v1.2.3`).
            *   Cấu hình các biến môi trường cần thiết (xem mục 3).
            *   Dokploy sẽ kéo image này về và chạy nó.
    *   **Chiến lược Build trực tiếp từ Source Code trên Dokploy (đơn giản hơn, có thể dùng cho dev/staging):**
        *   **Nixpacks/Railpack:** Đây là các build type mặc định hoặc mới của Dokploy. Dokploy sẽ tự động phát hiện dự án Node.js và build nó.
            *   Bạn cần cung cấp `NIXPACKS_START_CMD` (cho Nixpacks) hoặc `START_CMD` (cho Railpack) trong tab `Environment Variables` của Dokploy để chỉ định lệnh khởi chạy bot (ví dụ: `node src/index.js` hoặc `pnpm start`).
            *   **Lưu ý:** Việc build trực tiếp trên server có thể tiêu tốn tài nguyên. Đối với production, build image qua CI/CD thường tốt hơn.
        *   **Sử dụng Dockerfile trực tiếp từ Git:** Nếu bạn có `Dockerfile` trong repository, bạn có thể chọn `Source Type` là Git Provider, sau đó trong phần Build Type chọn `Dockerfile` và chỉ định đường dẫn đến Dockerfile đó. Dokploy sẽ build image trên server.

3.  **Quản lý Biến Môi trường trên Production với Dokploy:**
    *   Dokploy cung cấp tab **`Environment`** cho mỗi ứng dụng để quản lý các biến môi trường.
    *   **TUYỆT ĐỐI KHÔNG** hardcode Bot Token, API keys, database credentials, hoặc các thông tin nhạy cảm khác trong code.
    *   Tất cả các thông tin này **PHẢI** được đặt làm biến môi trường trong Dokploy UI.
    *   Dokploy cũng hỗ trợ **"Shared Environment Variables"** ở cấp độ Project, có thể được tham chiếu bởi các service/ứng dụng trong project đó (ví dụ: `${{project.BOT_TOKEN}}`).

4.  **Logging trên Production với Dokploy:**
    *   Dokploy cung cấp tab **`Logs`** cho mỗi ứng dụng, nơi bạn có thể xem log output (stdout/stderr) của bot theo thời gian thực.
    *   Đối với việc lưu trữ và phân tích log nâng cao, bạn vẫn có thể cấu hình bot để gửi log đến một hệ thống quản lý log tập trung (ví dụ: Sentry, Logtail, Papertrail, ELK Stack), nhưng log cơ bản đã có sẵn trên Dokploy để debug nhanh.

5.  **Giám sát (Monitoring) với Dokploy:**
    *   Dokploy cung cấp các biểu đồ giám sát cơ bản (CPU, Memory, Disk, Network) cho mỗi ứng dụng trong tab **`Monitoring`** (khi bot chạy trên cùng node với Dokploy UI hoặc với Dokploy Cloud).
    *   Đối với giám sát chuyên sâu, theo dõi logic nghiệp vụ của bot, hoặc thiết lập cảnh báo tùy chỉnh, việc tích hợp các công cụ giám sát chuyên dụng bên ngoài vẫn được khuyến nghị.

6.  **Cập nhật và Bảo trì Bot trên Dokploy:**
    *   **Khi sử dụng Docker image từ registry (khuyến nghị):**
        *   Quy trình cập nhật là CI/CD pipeline của bạn build một image mới với phiên bản code mới, push lên registry với tag mới (ví dụ: `v1.2.4`) hoặc cập nhật tag `latest`.
        *   Nếu đã cấu hình **Webhook Auto Deploy** trong Dokploy (tab `Deployments`, trỏ đến Docker Hub hoặc registry khác có hỗ trợ webhook), Dokploy có thể tự động phát hiện image mới và redeploy.
        *   Nếu không, bạn cần vào Dokploy UI, cập nhật tag image trong phần cấu hình Application (nếu dùng tag cụ thể) và nhấn "Redeploy".
    *   Dokploy hỗ trợ cấu hình **`Update Config`** và **`Rollback Config`** trong `Swarm Settings` (phần `Advanced` của Application). Điều này cho phép bạn kiểm soát cách các container được cập nhật (ví dụ: `Parallelism`, `Delay`, `FailureAction: rollback`, `Order: start-first`) và tự động rollback về phiên bản trước nếu health check thất bại sau khi cập nhật.
    *   Định kỳ cập nhật Node.js (qua Docker base image), thư viện discord.js, và các dependencies khác để vá lỗi bảo mật và nhận tính năng mới. Quá trình này bao gồm việc build lại Docker image.

7.  **CI/CD (Continuous Integration/Continuous Deployment) Mở rộng với Dokploy:**
    *   Như đã nhấn mạnh (Mục 11.3, 4.6 và chiến lược build ở trên), CI/CD pipeline là **rất khuyến nghị**.
    *   Pipeline này sẽ chịu trách nhiệm build Docker image, chạy tests, và push image lên Docker Registry.
    *   Dokploy sau đó sẽ deploy image này. Dokploy có **GitHub Action (`dokploy/dokploy-action@v1`)** hoặc **API method** để trigger deployment từ CI/CD pipeline của bạn, giúp tự động hóa hoàn toàn quy trình từ code commit đến production.

8.  **Lưu trữ Persistent (Persistent Storage) với Dokploy:**
    *   Nếu bot Discord của bạn cần lưu trữ dữ liệu persistent (ví dụ: một database SQLite nhỏ cho các tác vụ đơn giản, hoặc các file cấu hình đặc thù không tiện đưa vào biến môi trường), Dokploy hỗ trợ **`Volumes`** và **`File Mounts`** trong phần `Advanced -> Volumes/Mounts` của ứng dụng.
    *   Nên ưu tiên sử dụng **Docker-managed volumes** (`Volume Mount`) vì chúng dễ quản lý, backup và migrate hơn so với bind mounts (`Bind Mount`).

9.  **Networking và Ports với Dokploy:**
    *   Một bot Discord tiêu chuẩn thường chỉ thực hiện các kết nối ra ngoài đến Discord Gateway và các API khác, và **không cần expose bất kỳ port nào** ra internet.
    *   Trong Dokploy, bạn có thể không cần cấu hình gì trong mục `Ports` hoặc `Domains` của ứng dụng nếu bot không có giao diện web hoặc API phụ trợ.
    *   Nếu bot có một API/dashboard web phụ trợ, bạn sẽ cần cấu hình `Domains` trong Dokploy để Traefik (reverse proxy tích hợp của Dokploy) có thể định tuyến traffic đến port tương ứng của container bot.

Bằng cách tuân thủ các nguyên tắc này, các agent AI (và cả developer con người) có thể đóng góp vào dự án một cách nhất quán, giúp dự án dễ dàng mở rộng, bảo trì và đảm bảo chất lượng khi đưa lên production.

### 8. Thư mục `docs/` và `scripts/` (Tùy chọn nhưng khuyến khích)
*   `docs/`: Chứa các tài liệu chung của dự án không thuộc về một tính năng cụ thể (ví dụ: kiến trúc tổng quan, hướng dẫn đóng góp nâng cao).
*   `scripts/`: Chứa các kịch bản tự động hóa (ví dụ: script deploy, setup database, build).

## 18. Nguyên tắc về Tránh Circular Dependencies

*   **Tránh Circular Dependencies:** Cẩn thận trong việc thiết kế các module để tránh tình trạng import vòng tròn, gây khó khăn cho việc quản lý và có thể dẫn đến lỗi runtime.

## 19. Nguyên tắc về Sử dụng Test Server

*   **Sử dụng Test Server:** Đối với Integration Test và End-to-End (E2E) Test, nên sử dụng một Discord server riêng biệt (test server) để tránh ảnh hưởng đến server chính và có môi trường kiểm thử cô lập. E2E tests, mặc dù không thường xuyên bằng unit/integration tests, cũng nên được xem xét cho các luồng người dùng quan trọng.

## 20. Nguyên tắc về Tránh Global State

*   **Tránh Global State:** Hạn chế tối đa việc sử dụng biến toàn cục để lưu trữ trạng thái của bot. Thay vào đó, hãy truyền state qua tham số hàm, sử dụng instance của class, hoặc các giải pháp quản lý state chuyên dụng nếu cần thiết cho các trạng thái phức tạp.
*   **Giữ Code Đơn giản (KISS - Keep It Simple, Stupid):** Luôn cố gắng tìm giải pháp đơn giản nhất mà vẫn đáp ứng yêu cầu. Tránh over-engineering hoặc thêm các lớp trừu tượng không cần thiết.

## 21. Nguyên tắc về Quản lý Bộ nhớ (Memory Management)

*   **Quản lý Bộ nhớ (Memory Management):**
    *   Cẩn thận với các memory leaks (ví dụ: event listeners không được gỡ bỏ, closures giữ tham chiếu không cần thiết).
    *   Sử dụng các cấu trúc dữ liệu hiệu quả và giải phóng tài nguyên không còn sử dụng.
    *   Để ý đến việc xử lý lượng lớn dữ liệu có thể gây tốn bộ nhớ.
*   **Xử lý Lỗi từ Discord API:**
    *   Ngoài `try...catch` chung, lắng nghe các sự kiện lỗi từ Discord client (ví dụ: `client.on('error', console.error);`) để bắt các lỗi ở tầng kết nối hoặc API không được bắt bởi các xử lý cục bộ.

## 22. Nguyên tắc về Sharding và Lazy Loading

*   **Sharding (Cho Bot Lớn):** Khi bot phát triển và phục vụ số lượng lớn server/người dùng, cân nhắc việc triển khai sharding để phân tải và cải thiện hiệu năng/khả năng mở rộng. Đây là một kỹ thuật nâng cao.
*   **Lazy Loading (Commands/Events):** Đối với các bot có nhiều lệnh hoặc event handlers, cân nhắc việc chỉ load chúng khi cần thiết (ví dụ: khi lệnh được gọi lần đầu) thay vì load tất cả lúc khởi động, giúp giảm thời gian khởi động.

## Lời kết

Tài liệu này là nền tảng quan trọng để xây dựng một bot Discord chất lượng cao, dễ bảo trì và mở rộng. Việc tuân thủ nghiêm ngặt các quy tắc và hướng dẫn đã đề ra không chỉ giúp đảm bảo tính nhất quán trong toàn bộ dự án mà còn thúc đẩy sự hợp tác hiệu quả giữa các AI Agent và Developer.

Chúng tôi tin rằng với sự đồng lòng và cam kết tuân thủ những nguyên tắc này, dự án sẽ đạt được thành công như mong đợi. Mọi ý kiến đóng góp để cải thiện tài liệu này đều được hoan nghênh.
