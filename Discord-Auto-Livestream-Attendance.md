# Hệ thống Điểm danh Livestream Tự động

## 📋 Yêu cầu Cụ thể

### Auto-trigger workflow:
> **"Khi user admin bắt đầu vào room trò chuyện → livestream tự động bắt đầu. Khi admin thoát ra → gử<PERSON> thông báo cho admin biết ai đã tham gia và ai chưa + lưu database điểm danh"**

### Workflow tự động:
1. **Admin joins voice channel** → 🔴 Auto-start livestream tracking
2. **Students join/leave voice channel** → 📝 Real-time attendance recording  
3. **Admin leaves voice channel** → ✅ Auto-end + Report + Database save
4. **Admin notification** → 📊 Summary report với attendance data

## 🎯 Core Logic

### 1. **Auto-Detection System**
- Monitor voice state updates của **designated admin users**
- Detect voice channel join → trigger auto-start livestream session
- Detect voice channel leave → trigger auto-end + reporting
- Only specific voice channels đượ<PERSON> designated cho livestream

### 2. **Real-time Tracking**
- Track tất cả users trong voice channel khi admin có mặt
- Record join/leave timestamps cho mỗi user
- Calculate attendance duration và percentage
- Link Discord users với verified students database

### 3. **Automatic Reporting**
- Generate attendance summary khi admin leaves
- Send report đến admin via DM hoặc designated channel
- Save attendance records vào database
- Identify absent students và prepare notification queue

## 🗄️ Database Schema

### 1. **Auto Livestream Sessions**

#### `auto_livestream_sessions`
```sql
CREATE TABLE auto_livestream_sessions (
  id INT PRIMARY KEY AUTO_INCREMENT,
  
  -- Session identification
  guild_id VARCHAR(50) NOT NULL,
  voice_channel_id VARCHAR(50) NOT NULL,
  voice_channel_name VARCHAR(255),
  
  -- Admin tracking
  admin_discord_id VARCHAR(50) NOT NULL,
  admin_username VARCHAR(255),
  
  -- Session timing (tự động)
  session_start_time TIMESTAMP NOT NULL,
  session_end_time TIMESTAMP NULL,
  session_duration_seconds INT NULL,
  
  -- Auto-detection
  session_status ENUM('active', 'ended') DEFAULT 'active',
  is_auto_generated BOOLEAN DEFAULT TRUE,
  
  -- Course detection (optional)
  detected_course_id INT NULL,
  detected_course_name VARCHAR(255) NULL,
  
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_admin_discord_id (admin_discord_id),
  INDEX idx_voice_channel_id (voice_channel_id),
  INDEX idx_session_status (session_status),
  INDEX idx_session_start_time (session_start_time),
  
  FOREIGN KEY (detected_course_id) REFERENCES courses(id) ON DELETE SET NULL
);
```

### 2. **Auto Attendance Records** 

#### `auto_attendance_records`
```sql
CREATE TABLE auto_attendance_records (
  id INT PRIMARY KEY AUTO_INCREMENT,
  session_id INT NOT NULL,
  
  -- User identification
  discord_user_id VARCHAR(50) NOT NULL,
  discord_username VARCHAR(255),
  
  -- Attendance tracking
  first_join_time TIMESTAMP NULL,
  last_leave_time TIMESTAMP NULL,
  total_presence_seconds INT DEFAULT 0,
  
  -- Join/leave tracking
  join_count INT DEFAULT 0,
  leave_count INT DEFAULT 0,
  
  -- Calculated metrics
  attendance_percentage DECIMAL(5,2) DEFAULT 0.00,
  is_present BOOLEAN DEFAULT FALSE,
  
  -- Student verification
  is_verified_student BOOLEAN DEFAULT FALSE,
  student_email VARCHAR(255) NULL,
  student_fullname VARCHAR(255) NULL,
  student_course_name VARCHAR(255) NULL,
  student_tier_name VARCHAR(255) NULL,
  
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  UNIQUE KEY unique_session_user (session_id, discord_user_id),
  INDEX idx_session_id (session_id),
  INDEX idx_discord_user_id (discord_user_id),
  INDEX idx_is_present (is_present),
  INDEX idx_is_verified_student (is_verified_student),
  
  FOREIGN KEY (session_id) REFERENCES auto_livestream_sessions(id) ON DELETE CASCADE
);
```

### 3. **Admin Configuration**

#### `livestream_admin_config`
```sql
CREATE TABLE livestream_admin_config (
  id INT PRIMARY KEY AUTO_INCREMENT,
  guild_id VARCHAR(50) NOT NULL,
  
  -- Admin users có quyền trigger auto-livestream
  admin_discord_id VARCHAR(50) NOT NULL,
  admin_role VARCHAR(255),
  
  -- Voice channels được designated cho livestream
  allowed_voice_channel_id VARCHAR(50) NOT NULL,
  voice_channel_name VARCHAR(255),
  
  -- Course mapping (optional)
  default_course_id INT NULL,
  course_detection_method ENUM('channel_name', 'admin_role', 'manual') DEFAULT 'channel_name',
  
  -- Auto-settings
  min_session_duration_seconds INT DEFAULT 300,  -- 5 phút minimum
  attendance_threshold_percentage DECIMAL(5,2) DEFAULT 70.00,
  
  -- Notification settings
  report_channel_id VARCHAR(50) NULL,
  send_dm_to_admin BOOLEAN DEFAULT TRUE,
  notify_absent_students BOOLEAN DEFAULT TRUE,
  
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  UNIQUE KEY unique_admin_channel (admin_discord_id, allowed_voice_channel_id),
  INDEX idx_guild_id (guild_id),
  INDEX idx_admin_discord_id (admin_discord_id),
  INDEX idx_voice_channel_id (allowed_voice_channel_id),
  INDEX idx_is_active (is_active),
  
  FOREIGN KEY (default_course_id) REFERENCES courses(id) ON DELETE SET NULL
);
```

## 🏗️ Technical Implementation

### 1. **Core Event Handler**

#### Voice State Update với Auto-Detection
```javascript
// events/auto_livestream_voice_event.js
const AutoLivestreamService = require('../services/auto_livestream_service');
const logger = require('../../shared/utils/logger');

module.exports = {
  name: 'voiceStateUpdate',
  async execute(oldState, newState) {
    try {
      const userId = newState.id || oldState.id;
      const guild = newState.guild || oldState.guild;
      
      // Check admin join/leave events
      await handleAdminVoiceStateChange(oldState, newState, userId, guild);
      
      // Check student join/leave events during active sessions
      await handleStudentVoiceStateChange(oldState, newState, userId, guild);
      
    } catch (error) {
      logger.error('Error in auto livestream voice event:', error);
    }
  }
};

async function handleAdminVoiceStateChange(oldState, newState, userId, guild) {
  // Check if user là admin được cấu hình
  const adminConfig = await AutoLivestreamService.getAdminConfig(userId, guild.id);
  if (!adminConfig) return;
  
  // Admin joined voice channel
  if (!oldState.channel && newState.channel) {
    // Check if voice channel được allowed cho livestream
    if (adminConfig.allowed_voice_channels.includes(newState.channel.id)) {
      await AutoLivestreamService.startAutoSession({
        adminId: userId,
        guildId: guild.id,
        voiceChannelId: newState.channel.id,
        voiceChannelName: newState.channel.name,
        adminConfig: adminConfig
      });
      
      logger.info(`Auto-started livestream session: ${newState.channel.name} by admin ${userId}`);
    }
  }
  
  // Admin left voice channel  
  if (oldState.channel && !newState.channel) {
    // Check if có active session trong channel đó
    const activeSession = await AutoLivestreamService.getActiveSession(
      guild.id, 
      oldState.channel.id, 
      userId
    );
    
    if (activeSession) {
      await AutoLivestreamService.endAutoSession(activeSession.id);
      logger.info(`Auto-ended livestream session: ${activeSession.id} by admin ${userId}`);
    }
  }
}

async function handleStudentVoiceStateChange(oldState, newState, userId, guild) {
  // Check if có active livestream session nào
  const activeSessions = await AutoLivestreamService.getActiveSessions(guild.id);
  
  for (const session of activeSessions) {
    // Student joined livestream channel
    if (!oldState.channel && 
        newState.channel && 
        newState.channel.id === session.voice_channel_id) {
      
      await AutoLivestreamService.recordStudentJoin(session.id, userId);
      logger.debug(`Student ${userId} joined livestream session ${session.id}`);
    }
    
    // Student left livestream channel
    if (oldState.channel && 
        oldState.channel.id === session.voice_channel_id && 
        (!newState.channel || newState.channel.id !== session.voice_channel_id)) {
      
      await AutoLivestreamService.recordStudentLeave(session.id, userId);
      logger.debug(`Student ${userId} left livestream session ${session.id}`);
    }
  }
}
```

### 2. **Auto Livestream Service**

#### Core business logic
```javascript
// services/auto_livestream_service.js
const AutoLivestreamModel = require('../models/auto_livestream_model');
const StudentLookupService = require('./student_lookup_service');
const AttendanceCalculator = require('../utils/attendance_calculator');
const ReportGenerator = require('../utils/report_generator');
const logger = require('../../shared/utils/logger');

class AutoLivestreamService {
  
  /**
   * Bắt đầu auto livestream session khi admin join voice channel
   */
  static async startAutoSession({ adminId, guildId, voiceChannelId, voiceChannelName, adminConfig }) {
    try {
      // Check if đã có active session trong channel này
      const existingSession = await AutoLivestreamModel.getActiveSessionByChannel(
        guildId, 
        voiceChannelId
      );
      
      if (existingSession) {
        logger.warn(`Active session already exists in channel ${voiceChannelId}`);
        return existingSession;
      }
      
      // Detect course từ channel name hoặc admin config
      const detectedCourse = await this.detectCourse(voiceChannelName, adminConfig);
      
      // Tạo session mới
      const session = await AutoLivestreamModel.createSession({
        guildId,
        voiceChannelId,
        voiceChannelName,
        adminDiscordId: adminId,
        detectedCourseId: detectedCourse?.id,
        detectedCourseName: detectedCourse?.name,
        sessionStartTime: new Date()
      });
      
      // Log tất cả users hiện tại trong voice channel để bắt đầu tracking
      const voiceChannel = await this.getVoiceChannelById(guildId, voiceChannelId);
      if (voiceChannel && voiceChannel.members.size > 0) {
        for (const [memberId, member] of voiceChannel.members) {
          if (memberId !== adminId) { // Exclude admin
            await this.recordStudentJoin(session.id, memberId);
          }
        }
      }
      
      logger.info(`Auto-started livestream session ${session.id} in ${voiceChannelName}`);
      return session;
      
    } catch (error) {
      logger.error('Error starting auto session:', error);
      throw error;
    }
  }
  
  /**
   * Kết thúc auto livestream session khi admin leave voice channel
   */
  static async endAutoSession(sessionId) {
    try {
      const session = await AutoLivestreamModel.getSessionById(sessionId);
      if (!session) {
        throw new Error(`Session ${sessionId} not found`);
      }
      
      const endTime = new Date();
      const sessionDuration = Math.floor((endTime - new Date(session.session_start_time)) / 1000);
      
      // Check minimum duration requirement
      const adminConfig = await AutoLivestreamModel.getAdminConfigByAdmin(session.admin_discord_id);
      if (sessionDuration < (adminConfig?.min_session_duration_seconds || 300)) {
        logger.info(`Session ${sessionId} too short (${sessionDuration}s), not processing attendance`);
        await AutoLivestreamModel.deleteSession(sessionId);
        return null;
      }
      
      // End session
      await AutoLivestreamModel.endSession(sessionId, endTime, sessionDuration);
      
      // Calculate final attendance cho tất cả participants
      await this.calculateFinalAttendance(sessionId, sessionDuration);
      
      // Generate và send report cho admin
      await this.generateAndSendReport(sessionId);
      
      // Schedule notifications cho absent students (nếu enabled)
      if (adminConfig?.notify_absent_students) {
        await this.scheduleAbsentNotifications(sessionId);
      }
      
      logger.info(`Auto-ended livestream session ${sessionId}, duration: ${sessionDuration}s`);
      return session;
      
    } catch (error) {
      logger.error('Error ending auto session:', error);
      throw error;
    }
  }
  
  /**
   * Record student join voice channel
   */
  static async recordStudentJoin(sessionId, discordUserId) {
    try {
      const joinTime = new Date();
      
      // Get hoặc create attendance record
      let attendanceRecord = await AutoLivestreamModel.getAttendanceRecord(sessionId, discordUserId);
      
      if (!attendanceRecord) {
        // Identify student từ verified database
        const session = await AutoLivestreamModel.getSessionById(sessionId);
        const studentInfo = await StudentLookupService.identifyStudent(
          discordUserId, 
          session.detected_course_id
        );
        
        // Create new attendance record
        attendanceRecord = await AutoLivestreamModel.createAttendanceRecord({
          sessionId,
          discordUserId,
          firstJoinTime: joinTime,
          isVerifiedStudent: studentInfo.isEnrolled,
          studentEmail: studentInfo.email,
          studentFullname: studentInfo.fullname,
          studentCourseName: studentInfo.courseName,
          studentTierName: studentInfo.tierName
        });
      } else {
        // Update existing record - nếu user join lại
        if (!attendanceRecord.first_join_time) {
          await AutoLivestreamModel.updateAttendanceRecord(attendanceRecord.id, {
            firstJoinTime: joinTime
          });
        }
      }
      
      // Increment join count
      await AutoLivestreamModel.incrementJoinCount(attendanceRecord.id);
      
      logger.debug(`Recorded join for user ${discordUserId} in session ${sessionId}`);
      
    } catch (error) {
      logger.error('Error recording student join:', error);
    }
  }
  
  /**
   * Record student leave voice channel
   */
  static async recordStudentLeave(sessionId, discordUserId) {
    try {
      const leaveTime = new Date();
      
      const attendanceRecord = await AutoLivestreamModel.getAttendanceRecord(sessionId, discordUserId);
      if (!attendanceRecord) {
        logger.warn(`No attendance record found for user ${discordUserId} in session ${sessionId}`);
        return;
      }
      
      // Calculate presence duration từ last join đến leave
      const lastJoinTime = attendanceRecord.last_join_time || attendanceRecord.first_join_time;
      if (lastJoinTime) {
        const presenceDuration = Math.floor((leaveTime - new Date(lastJoinTime)) / 1000);
        
        // Update attendance record
        await AutoLivestreamModel.updateAttendanceRecord(attendanceRecord.id, {
          lastLeaveTime: leaveTime,
          totalPresenceSeconds: attendanceRecord.total_presence_seconds + presenceDuration
        });
      }
      
      // Increment leave count
      await AutoLivestreamModel.incrementLeaveCount(attendanceRecord.id);
      
      logger.debug(`Recorded leave for user ${discordUserId} in session ${sessionId}`);
      
    } catch (error) {
      logger.error('Error recording student leave:', error);
    }
  }
  
  /**
   * Calculate final attendance percentages
   */
  static async calculateFinalAttendance(sessionId, sessionDurationSeconds) {
    try {
      const attendanceRecords = await AutoLivestreamModel.getSessionAttendanceRecords(sessionId);
      const adminConfig = await this.getSessionAdminConfig(sessionId);
      
      for (const record of attendanceRecords) {
        // Calculate attendance percentage
        const attendancePercentage = AttendanceCalculator.calculatePercentage(
          record.total_presence_seconds,
          sessionDurationSeconds
        );
        
        // Determine if present based on threshold
        const isPresent = attendancePercentage >= (adminConfig?.attendance_threshold_percentage || 70);
        
        // Update record với calculated values
        await AutoLivestreamModel.updateAttendanceRecord(record.id, {
          attendancePercentage,
          isPresent
        });
      }
      
      logger.info(`Calculated final attendance for ${attendanceRecords.length} participants in session ${sessionId}`);
      
    } catch (error) {
      logger.error('Error calculating final attendance:', error);
    }
  }
  
  /**
   * Generate và send report cho admin
   */
  static async generateAndSendReport(sessionId) {
    try {
      const session = await AutoLivestreamModel.getSessionById(sessionId);
      const attendanceRecords = await AutoLivestreamModel.getSessionAttendanceRecords(sessionId);
      const adminConfig = await AutoLivestreamModel.getAdminConfigByAdmin(session.admin_discord_id);
      
      // Generate comprehensive report
      const report = await ReportGenerator.generateSessionReport(session, attendanceRecords);
      
      // Send DM to admin
      if (adminConfig?.send_dm_to_admin) {
        await this.sendAdminDMReport(session.admin_discord_id, report);
      }
      
      // Send to report channel
      if (adminConfig?.report_channel_id) {
        await this.sendChannelReport(adminConfig.report_channel_id, report);
      }
      
      logger.info(`Sent attendance report for session ${sessionId} to admin ${session.admin_discord_id}`);
      
    } catch (error) {
      logger.error('Error generating and sending report:', error);
    }
  }
  
  /**
   * Detect course từ voice channel name hoặc admin config
   */
  static async detectCourse(voiceChannelName, adminConfig) {
    try {
      // Method 1: Từ admin config default course
      if (adminConfig.default_course_id) {
        const course = await AutoLivestreamModel.getCourseById(adminConfig.default_course_id);
        if (course) return course;
      }
      
      // Method 2: Pattern matching từ channel name
      const coursePatterns = {
        'hoa-10': /hóa.*10|lớp.*10|grade.*10/i,
        'hoa-11': /hóa.*11|lớp.*11|grade.*11/i,
        'hoa-12': /hóa.*12|lớp.*12|grade.*12/i
      };
      
      for (const [courseSlug, pattern] of Object.entries(coursePatterns)) {
        if (pattern.test(voiceChannelName)) {
          const course = await AutoLivestreamModel.getCourseBySlug(courseSlug);
          if (course) return course;
        }
      }
      
      return null;
    } catch (error) {
      logger.error('Error detecting course:', error);
      return null;
    }
  }
}

module.exports = AutoLivestreamService;
```

### 3. **Report Generator**

#### Tạo báo cáo attendance chi tiết
```javascript
// utils/report_generator.js
const { EmbedBuilder } = require('discord.js');

class ReportGenerator {
  
  static async generateSessionReport(session, attendanceRecords) {
    const stats = this.calculateStats(session, attendanceRecords);
    
    // Main report embed
    const reportEmbed = new EmbedBuilder()
      .setTitle('📊 Báo cáo Điểm danh Livestream')
      .setDescription(`**${session.voice_channel_name}**\n${session.detected_course_name || 'Khóa học không xác định'}`)
      .addFields([
        { 
          name: '⏰ Thời gian', 
          value: `${this.formatTime(session.session_start_time)} - ${this.formatTime(session.session_end_time)}`, 
          inline: false 
        },
        { 
          name: '⏱️ Thời lượng', 
          value: this.formatDuration(session.session_duration_seconds), 
          inline: true 
        },
        { 
          name: '👥 Tổng tham gia', 
          value: `${stats.totalParticipants} người`, 
          inline: true 
        },
        { 
          name: '🎓 Học sinh verified', 
          value: `${stats.verifiedStudents} người`, 
          inline: true 
        },
        { 
          name: '✅ Điểm danh đạt', 
          value: `${stats.presentStudents} người`, 
          inline: true 
        },
        { 
          name: '❌ Vắng mặt', 
          value: `${stats.absentStudents} người`, 
          inline: true 
        },
        { 
          name: '📈 Tỷ lệ tham gia', 
          value: `${stats.attendanceRate}%`, 
          inline: true 
        }
      ])
      .setColor(stats.attendanceRate >= 80 ? 0x00ff00 : stats.attendanceRate >= 60 ? 0xffa500 : 0xff0000)
      .setTimestamp();
    
    // Detailed lists
    const presentList = attendanceRecords
      .filter(r => r.is_present)
      .map((r, i) => `${i + 1}. ${r.student_fullname || r.discord_username} (${r.attendance_percentage}%)`)
      .join('\n');
      
    const absentList = attendanceRecords
      .filter(r => r.is_verified_student && !r.is_present)
      .map((r, i) => `${i + 1}. ${r.student_fullname} (${r.attendance_percentage}%)`)
      .join('\n');
    
    return {
      embeds: [reportEmbed],
      content: `🔔 **Báo cáo điểm danh tự động**\n\n` +
               `**✅ Có mặt (${stats.presentStudents}):**\n${presentList || 'Không có'}\n\n` +
               `**❌ Vắng mặt (${stats.absentStudents}):**\n${absentList || 'Không có'}\n\n` +
               `_Báo cáo được tạo tự động khi admin rời khỏi voice channel._`
    };
  }
  
  static calculateStats(session, attendanceRecords) {
    const totalParticipants = attendanceRecords.length;
    const verifiedStudents = attendanceRecords.filter(r => r.is_verified_student).length;
    const presentStudents = attendanceRecords.filter(r => r.is_present).length;
    const absentStudents = attendanceRecords.filter(r => r.is_verified_student && !r.is_present).length;
    
    const attendanceRate = verifiedStudents > 0 
      ? Math.round((presentStudents / verifiedStudents) * 100) 
      : 0;
    
    return {
      totalParticipants,
      verifiedStudents,
      presentStudents,
      absentStudents,
      attendanceRate
    };
  }
  
  static formatTime(timestamp) {
    return new Date(timestamp).toLocaleString('vi-VN', {
      timeZone: 'Asia/Ho_Chi_Minh',
      hour: '2-digit',
      minute: '2-digit',
      day: '2-digit',
      month: '2-digit'
    });
  }
  
  static formatDuration(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  }
}

module.exports = ReportGenerator;
```

## 🎛️ Admin Configuration Commands

### Setup Commands cho Admin

#### `/auto-livestream setup`
```javascript
// commands/auto_livestream_setup.js
const { SlashCommandBuilder, PermissionFlagsBits } = require('discord.js');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('auto-livestream')
    .setDescription('Cấu hình hệ thống điểm danh livestream tự động')
    .setDefaultMemberPermissions(PermissionFlagsBits.Administrator)
    .addSubcommand(subcommand =>
      subcommand
        .setName('setup')
        .setDescription('Thiết lập admin và voice channel cho auto-tracking')
        .addUserOption(option =>
          option.setName('admin')
            .setDescription('User admin sẽ trigger auto-livestream')
            .setRequired(true))
        .addChannelOption(option =>
          option.setName('voice_channel')
            .setDescription('Voice channel được designated cho livestream')
            .setRequired(true))
        .addStringOption(option =>
          option.setName('course')
            .setDescription('Khóa học mặc định')
            .setRequired(false)
            .addChoices(
              { name: 'Hóa học lớp 10', value: 'hoa-10' },
              { name: 'Hóa học lớp 11', value: 'hoa-11' },
              { name: 'Hóa học lớp 12', value: 'hoa-12' }
            ))
        .addChannelOption(option =>
          option.setName('report_channel')
            .setDescription('Channel nhận báo cáo attendance')
            .setRequired(false))
        .addIntegerOption(option =>
          option.setName('min_duration')
            .setDescription('Thời gian tối thiểu (phút) để tính attendance')
            .setRequired(false)
            .setMinValue(1)
            .setMaxValue(60))
        .addNumberOption(option =>
          option.setName('attendance_threshold')
            .setDescription('Ngưỡng % để tính có mặt (mặc định 70%)')
            .setRequired(false)
            .setMinValue(50)
            .setMaxValue(100)))
    .addSubcommand(subcommand =>
      subcommand
        .setName('list')
        .setDescription('Xem danh sách cấu hình hiện tại'))
    .addSubcommand(subcommand =>
      subcommand
        .setName('remove')
        .setDescription('Xóa cấu hình auto-livestream')
        .addUserOption(option =>
          option.setName('admin')
            .setDescription('Admin cần xóa cấu hình')
            .setRequired(true))
        .addChannelOption(option =>
          option.setName('voice_channel')
            .setDescription('Voice channel cần xóa')
            .setRequired(true))),

  async execute(interaction) {
    const subcommand = interaction.options.getSubcommand();
    
    if (subcommand === 'setup') {
      const admin = interaction.options.getUser('admin');
      const voiceChannel = interaction.options.getChannel('voice_channel');
      const course = interaction.options.getString('course');
      const reportChannel = interaction.options.getChannel('report_channel');
      const minDuration = interaction.options.getInteger('min_duration') || 5;
      const attendanceThreshold = interaction.options.getNumber('attendance_threshold') || 70;
      
      // Validate voice channel type
      if (voiceChannel.type !== 2) { // GuildVoice
        return interaction.reply({ 
          content: '❌ Channel phải là voice channel!', 
          ephemeral: true 
        });
      }
      
      try {
        await AutoLivestreamService.setupAdminConfig({
          guildId: interaction.guild.id,
          adminDiscordId: admin.id,
          allowedVoiceChannelId: voiceChannel.id,
          voiceChannelName: voiceChannel.name,
          defaultCourse: course,
          reportChannelId: reportChannel?.id,
          minSessionDurationSeconds: minDuration * 60,
          attendanceThresholdPercentage: attendanceThreshold
        });
        
        await interaction.reply({
          embeds: [{
            title: '✅ Cấu hình Auto-Livestream thành công!',
            fields: [
              { name: '👤 Admin', value: admin.toString(), inline: true },
              { name: '🎤 Voice Channel', value: voiceChannel.toString(), inline: true },
              { name: '📚 Khóa học mặc định', value: course || 'Không xác định', inline: true },
              { name: '📊 Report Channel', value: reportChannel?.toString() || 'DM Admin', inline: true },
              { name: '⏱️ Thời gian tối thiểu', value: `${minDuration} phút`, inline: true },
              { name: '📈 Ngưỡng điểm danh', value: `${attendanceThreshold}%`, inline: true }
            ],
            description: `Từ giờ, khi **${admin.username}** join vào **${voiceChannel.name}**, hệ thống sẽ tự động bắt đầu tracking attendance!`,
            color: 0x00ff00
          }]
        });
        
      } catch (error) {
        logger.error('Error setting up auto-livestream:', error);
        await interaction.reply({ 
          content: '❌ Có lỗi khi thiết lập cấu hình. Vui lòng thử lại!', 
          ephemeral: true 
        });
      }
    }
    
    // ... other subcommands
  }
};
```

## 📱 Real-time Admin Notifications

### Auto-generated Reports

#### DM Report cho Admin
```javascript
// Automatic DM when admin leaves voice channel
async function sendAdminDMReport(adminDiscordId, report) {
  try {
    const admin = await client.users.fetch(adminDiscordId);
    
    await admin.send({
      content: `🔔 **Báo cáo điểm danh tự động**\n_Vừa kết thúc livestream_`,
      ...report
    });
    
    logger.info(`Sent auto-report DM to admin ${adminDiscordId}`);
  } catch (error) {
    logger.error('Error sending admin DM report:', error);
  }
}
```

#### Channel Report
```javascript
// Auto-report to designated channel
async function sendChannelReport(channelId, report) {
  try {
    const channel = await client.channels.fetch(channelId);
    
    await channel.send({
      content: `📊 **Báo cáo điểm danh tự động** - ${new Date().toLocaleString('vi-VN')}`,
      ...report
    });
    
    logger.info(`Sent auto-report to channel ${channelId}`);
  } catch (error) {
    logger.error('Error sending channel report:', error);
  }
}
```

## 🎯 Example Workflow

### Hoàn chỉnh Auto Process:

#### 1. **Setup** (One-time)
```bash
/auto-livestream setup admin:@ThayHoa voice_channel:#livestream-hoa12 course:"hoa-12" 
report_channel:#admin-reports min_duration:5 attendance_threshold:70

✅ Cấu hình thành công!
👤 Admin: @ThayHoa
🎤 Voice Channel: #livestream-hoa12  
📚 Khóa học: Hóa học lớp 12
📊 Report Channel: #admin-reports
⏱️ Thời gian tối thiểu: 5 phút
📈 Ngưỡng điểm danh: 70%
```

#### 2. **Auto-Start** (When Admin Joins)
```
🔴 [Auto-message in #admin-reports]
LIVESTREAM BẮT ĐẦU TỰ ĐỘNG

👤 Admin: @ThayHoa  
📍 Channel: #livestream-hoa12
🎯 Khóa học: Hóa học lớp 12
⏰ Bắt đầu: 19:00 hôm nay
🎬 Đang tracking attendance...
```

#### 3. **Auto-End Report** (When Admin Leaves)
```
📊 [Auto-DM to Admin + Channel Message]
Báo cáo Điểm danh Livestream

📍 #livestream-hoa12 - Hóa học lớp 12
⏰ 19:00 - 20:30 (1h 30m)
👥 Tổng tham gia: 18 người
🎓 Học sinh verified: 15 người  
✅ Điểm danh đạt: 12 người
❌ Vắng mặt: 3 người
📈 Tỷ lệ tham gia: 80%

✅ Có mặt (12):
1. Nguyễn Văn A (95%)
2. Trần Thị B (87%)
3. Lê Văn C (78%)
...

❌ Vắng mặt (3):  
1. Phạm Thị D (45%)
2. Hoàng Văn E (30%)
3. Đỗ Thị F (0% - không tham gia)

_Báo cáo được tạo tự động khi admin rời voice channel._
```

#### 4. **Database Saved**
```sql
-- Tất cả attendance data được lưu vào:
SELECT 
  s.voice_channel_name,
  s.session_start_time,
  s.session_duration_seconds,
  a.student_fullname,
  a.attendance_percentage,
  a.is_present
FROM auto_livestream_sessions s
JOIN auto_attendance_records a ON s.id = a.session_id
WHERE s.admin_discord_id = 'ADMIN_ID'
ORDER BY s.session_start_time DESC;
```

---

**Đây là hệ thống hoàn toàn tự động như bạn yêu cầu!** 🚀 

✅ **Admin join voice channel** → Auto-start tracking  
✅ **Students join/leave** → Real-time attendance recording  
✅ **Admin leave voice channel** → Auto-end + Report + Database save  
✅ **Zero manual commands** required during livestream  

**Bạn có muốn tôi bắt đầu implement hệ thống này không?** 🎯 