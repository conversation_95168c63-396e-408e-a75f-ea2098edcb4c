# Phân tích Database Schema - Discord Bot Ông Ba Dạy Hoá

## Tổng quan Database

### Loại Database: MySQL
- **M<PERSON>c đích**: <PERSON><PERSON><PERSON> trữ thông tin học sinh, đơ<PERSON> hàng, kh<PERSON>a học và mã kích hoạt từ website Ông Ba Dạy Hoá
- **Kết nối**: MySQL connection pool với configuration riêng cho feature `verify`
- **Timezone**: UTC+7 (Việt Nam)

## Cấu trúc Database Schema

### 1. Bảng Chính (Main Tables)

#### A. `up_users` - Thông tin Học sinh
```sql
CREATE TABLE up_users (
  id INT PRIMARY KEY AUTO_INCREMENT,
  email VARCHAR(255) UNIQUE NOT NULL,
  fullname VARCHAR(255) NOT NULL,
  phone VARCHAR(20),
  gender ENUM('male', 'female', 'other'),
  date DATE,
  where_you_know_website TEXT,
  user_discord_id VARCHAR(50) NULL,  -- Discord ID khi đã xác thực
  parents_phone VARCHAR(20) NULL,    -- SĐT phụ huynh
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

**Mục đích**: 
- Lưu trữ thông tin cá nhân của học sinh
- Liên kết với Discord ID sau khi xác thực
- Tracking nguồn biết website

#### B. `activation_codes` - Mã Kích hoạt
```sql
CREATE TABLE activation_codes (
  id INT PRIMARY KEY AUTO_INCREMENT,
  code VARCHAR(7) UNIQUE NOT NULL,   -- Format: 7 ký tự A-Z, 0-9
  activated_at TIMESTAMP NULL,       -- Thời điểm kích hoạt Discord
  discord_status ENUM('0', '1') DEFAULT '0',  -- Trạng thái Discord
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**Mục đích**:
- Mỗi đơn hàng có mã kích hoạt riêng
- Tracking trạng thái kích hoạt Discord
- Đảm bảo mỗi mã chỉ dùng 1 lần

#### C. `orders` - Đơn hàng
```sql
CREATE TABLE orders (
  id INT PRIMARY KEY AUTO_INCREMENT,
  order_code VARCHAR(50) UNIQUE NOT NULL,
  payos_order_code VARCHAR(100),
  order_date DATETIME,
  total_amount DECIMAL(10,2),
  payment_status ENUM('pending', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
  delivery_address TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

**Mục đích**:
- Quản lý thông tin đơn hàng
- Tracking trạng thái thanh toán
- Chỉ đơn hàng `completed` mới được xác thực Discord

#### D. `courses` - Khóa học
```sql
CREATE TABLE courses (
  id INT PRIMARY KEY AUTO_INCREMENT,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  slug VARCHAR(255) UNIQUE,
  status ENUM('active', 'inactive') DEFAULT 'active',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

**Mục đích**:
- Danh mục các khóa học available
- Mỗi khóa học có thể có nhiều tier

#### E. `course_tiers` - Cấp độ Khóa học
```sql
CREATE TABLE course_tiers (
  id INT PRIMARY KEY AUTO_INCREMENT,
  tier_name VARCHAR(100) NOT NULL,           -- 'Standard', 'Premium', 'VIP'
  tier_type VARCHAR(50),                     -- Additional categorization
  price DECIMAL(10,2),
  discord_role_id VARCHAR(50) NULL,          -- Discord Role ID tương ứng
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

**Mục đích**:
- Định nghĩa các gói/cấp độ của khóa học
- **Quan trọng**: `discord_role_id` để map với Discord role

### 2. Bảng Liên kết (Junction Tables)

#### A. `orders_users_permissions_user_lnk`
```sql
CREATE TABLE orders_users_permissions_user_lnk (
  id INT PRIMARY KEY AUTO_INCREMENT,
  order_id INT NOT NULL,
  user_id INT NOT NULL,
  FOREIGN KEY (order_id) REFERENCES orders(id),
  FOREIGN KEY (user_id) REFERENCES up_users(id),
  UNIQUE KEY unique_order_user (order_id, user_id)
);
```
**Quan hệ**: `orders` ←→ `up_users` (Many-to-Many)

#### B. `orders_activation_codes_lnk`
```sql
CREATE TABLE orders_activation_codes_lnk (
  id INT PRIMARY KEY AUTO_INCREMENT,
  order_id INT NOT NULL,
  activation_code_id INT NOT NULL,
  FOREIGN KEY (order_id) REFERENCES orders(id),
  FOREIGN KEY (activation_code_id) REFERENCES activation_codes(id),
  UNIQUE KEY unique_order_code (order_id, activation_code_id)
);
```
**Quan hệ**: `orders` ←→ `activation_codes` (One-to-One thường)

#### C. `orders_course_lnk`
```sql
CREATE TABLE orders_course_lnk (
  id INT PRIMARY KEY AUTO_INCREMENT,
  order_id INT NOT NULL,
  course_id INT NOT NULL,
  FOREIGN KEY (order_id) REFERENCES orders(id),
  FOREIGN KEY (course_id) REFERENCES courses(id),
  UNIQUE KEY unique_order_course (order_id, course_id)
);
```
**Quan hệ**: `orders` ←→ `courses` (Many-to-Many)

#### D. `orders_course_tier_lnk`
```sql
CREATE TABLE orders_course_tier_lnk (
  id INT PRIMARY KEY AUTO_INCREMENT,
  order_id INT NOT NULL,
  course_tier_id INT NOT NULL,
  FOREIGN KEY (order_id) REFERENCES orders(id),
  FOREIGN KEY (course_tier_id) REFERENCES course_tiers(id),
  UNIQUE KEY unique_order_tier (order_id, course_tier_id)
);
```
**Quan hệ**: `orders` ←→ `course_tiers` (Many-to-Many)

#### E. `course_tiers_course_lnk`
```sql
CREATE TABLE course_tiers_course_lnk (
  id INT PRIMARY KEY AUTO_INCREMENT,
  course_tier_id INT NOT NULL,
  course_id INT NOT NULL,
  FOREIGN KEY (course_tier_id) REFERENCES course_tiers(id),
  FOREIGN KEY (course_id) REFERENCES courses(id),
  UNIQUE KEY unique_tier_course (course_tier_id, course_id)
);
```
**Quan hệ**: `course_tiers` ←→ `courses` (Many-to-Many)

## Database Relationships Diagram

```mermaid
erDiagram
    up_users ||--o{ orders_users_permissions_user_lnk : "có nhiều đơn hàng"
    orders ||--o{ orders_users_permissions_user_lnk : "của nhiều user"
    
    orders ||--|| orders_activation_codes_lnk : "có mã kích hoạt"
    activation_codes ||--|| orders_activation_codes_lnk : "thuộc đơn hàng"
    
    orders ||--o{ orders_course_lnk : "mua nhiều khóa học"
    courses ||--o{ orders_course_lnk : "trong nhiều đơn hàng"
    
    orders ||--o{ orders_course_tier_lnk : "có nhiều tier"
    course_tiers ||--o{ orders_course_tier_lnk : "trong nhiều đơn hàng"
    
    courses ||--o{ course_tiers_course_lnk : "có nhiều tier"
    course_tiers ||--o{ course_tiers_course_lnk : "thuộc nhiều course"
    
    up_users {
        int id PK
        string email UK
        string fullname
        string phone
        string user_discord_id "NULL khi chưa xác thực"
        string parents_phone
    }
    
    activation_codes {
        int id PK
        string code UK "7 ký tự A-Z,0-9"
        timestamp activated_at "NULL khi chưa kích hoạt"
        enum discord_status "0/1"
    }
    
    orders {
        int id PK
        string order_code UK
        string payos_order_code
        decimal total_amount
        enum payment_status "pending/completed/failed/cancelled"
    }
    
    courses {
        int id PK
        string title
        string slug UK
        enum status "active/inactive"
    }
    
    course_tiers {
        int id PK
        string tier_name "Standard/Premium/VIP"
        string tier_type
        decimal price
        string discord_role_id "Discord Role ID"
    }
```

## Business Logic & Data Flow

### 1. Luồng Mua khóa học → Xác thực Discord

```
1. User mua khóa học trên website
   ↓
2. Tạo record trong `orders` (payment_status = 'pending')
   ↓
3. Link order với user, course, tier, activation_code
   ↓
4. Thanh toán thành công → payment_status = 'completed'
   ↓
5. User nhận mã kích hoạt → Xác thực trong Discord
   ↓
6. Bot check DB → Cập nhật discord_status, user_discord_id
   ↓
7. User nhận Discord role tương ứng course tier
```

### 2. Query Pattern Chính trong Bot

#### A. Verify Student (Core Query)
```sql
SELECT
  u.id AS user_id,
  u.email,
  u.fullname,
  ac.id AS activation_code_id,
  ac.code,
  ac.activated_at,
  ac.discord_status,
  ct.discord_role_id,
  c.title AS course_title,
  ct.tier_name,
  o.id AS order_id
FROM activation_codes ac
JOIN orders_activation_codes_lnk oac ON ac.id = oac.activation_code_id
JOIN orders o ON oac.order_id = o.id
JOIN orders_users_permissions_user_lnk oupl ON o.id = oupl.order_id
JOIN up_users u ON oupl.user_id = u.id
JOIN orders_course_lnk ocl ON o.id = ocl.order_id
JOIN courses c ON ocl.course_id = c.id
JOIN orders_course_tier_lnk octl ON o.id = octl.order_id
JOIN course_tiers ct ON octl.course_tier_id = ct.id
WHERE u.email = ? AND ac.code = ? AND o.payment_status = 'completed'
```

#### B. Get All Student Courses
```sql
SELECT
  c.id AS course_id,
  c.title AS course_name,
  ct.discord_role_id,
  ct.tier_name,
  ac.discord_status
FROM up_users u
JOIN orders_users_permissions_user_lnk oupl ON u.id = oupl.user_id
JOIN orders o ON oupl.order_id = o.id
JOIN orders_course_lnk ocl ON o.id = ocl.order_id
JOIN courses c ON ocl.course_id = c.id
JOIN orders_course_tier_lnk octl ON o.id = octl.order_id
JOIN course_tiers ct ON octl.course_tier_id = ct.id
JOIN orders_activation_codes_lnk oac ON o.id = oac.order_id
JOIN activation_codes ac ON oac.activation_code_id = ac.id
WHERE u.email = ? AND o.payment_status = 'completed'
```

## Key Features của Database Design

### 1. **Flexibility**
- Hỗ trợ nhiều courses per order
- Nhiều tiers per course
- Phân tách rõ business entities

### 2. **Discord Integration**
- `course_tiers.discord_role_id`: Map trực tiếp với Discord roles
- `up_users.user_discord_id`: Track Discord account đã link
- `activation_codes.discord_status`: Track trạng thái xác thực Discord

### 3. **Audit Trail**
- `activated_at`: Timestamp chính xác khi xác thực
- `payment_status`: Tracking trạng thái payment
- Timestamps trong tất cả bảng chính

### 4. **Data Integrity**
- Foreign key constraints
- Unique constraints cho business keys
- Junction tables với composite unique keys

### 5. **Security Considerations**
- Validation email format trong application
- Activation code format: 7 characters A-Z,0-9
- Discord ID validation
- Prevention SQL injection thông qua parameterized queries

## Configuration & Connection

### Environment Variables
```bash
VERIFY_DB_HOST=localhost
VERIFY_DB_PORT=3306
VERIFY_DB_USER=username
VERIFY_DB_PASSWORD=password
VERIFY_DB_NAME=database_name
```

### Connection Pool Settings
```javascript
{
  host: process.env.VERIFY_DB_HOST,
  port: process.env.VERIFY_DB_PORT || 3306,
  user: process.env.VERIFY_DB_USER,
  password: process.env.VERIFY_DB_PASSWORD,
  database: process.env.VERIFY_DB_NAME,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  timezone: '+07:00'  // Vietnam timezone
}
```

## Monitoring & Analytics Queries

### 1. Thống kê học sinh theo khóa học
```sql
SELECT c.title AS course_title, COUNT(DISTINCT u.id) AS student_count
FROM courses c
JOIN orders_course_lnk ocl ON c.id = ocl.course_id
JOIN orders o ON ocl.order_id = o.id
JOIN orders_users_permissions_user_lnk oupl ON o.id = oupl.order_id
JOIN up_users u ON oupl.user_id = u.id
WHERE o.payment_status = 'completed'
GROUP BY c.id
ORDER BY student_count DESC
```

### 2. Tracking Discord verification rate
```sql
SELECT 
  COUNT(CASE WHEN user_discord_id IS NOT NULL THEN 1 END) AS verified_users,
  COUNT(*) AS total_users,
  ROUND(COUNT(CASE WHEN user_discord_id IS NOT NULL THEN 1 END) * 100.0 / COUNT(*), 2) AS verification_rate
FROM up_users u
WHERE EXISTS (
  SELECT 1 FROM orders_users_permissions_user_lnk oupl 
  JOIN orders o ON oupl.order_id = o.id 
  WHERE oupl.user_id = u.id AND o.payment_status = 'completed'
)
```

## Kết luận

Database schema của Discord bot được thiết kế:
- **Normalized**: Tách biệt entities rõ ràng, tránh redundancy
- **Flexible**: Hỗ trợ business model phức tạp (multiple courses, tiers)
- **Discord-ready**: Built-in integration với Discord roles
- **Scalable**: Junction tables cho relationships many-to-many
- **Auditable**: Tracking timestamps và status changes

Schema này phù hợp hoàn hảo cho business model của Ông Ba Dạy Hoá và đảm bảo tính toàn vẹn dữ liệu trong quá trình integration với Discord bot. 