# Bot Discord Ông Ba Dạy Hoá

Bot Discord được phát triển cho cộng đồng Ông Ba Dạy Hoá, với cấu trúc module hoá theo t<PERSON>h năng, d<PERSON> bảo trì và mở rộng.

## Cài đặt và Thiết lập

### Yêu cầu
- Node.js 16.9.0 trở lên
- NPM hoặc Yarn
- MySQL Server (remote hoặc local)

### Các bước cài đặt

1. Clone repository về máy local:
```bash
git clone <repo-url>
cd ongbadayhoa-discord-bot-v2
```

2. Cài đặt các dependencies:
```bash
npm install
# Hoặc
yarn install
```

3. Sao chép file `.env.example` thành `.env` và cập nhật các biến môi trường:
```bash
cp .env.example .env
# Sau đó mở file .env và điền các thông tin cần thiết
```

4. <PERSON><PERSON><PERSON> hình kết nối database:
   - <PERSON><PERSON><PERSON> bảo MySQL server đã được cài đặt và chạy
   - Cập nhật thông tin kết nối trong file `.env`:
     - Cho tính năng verify (kết nối đến database của website):
       - `VERIFY_DB_HOST`: địa chỉ MySQL server
       - `VERIFY_DB_PORT`: cổng MySQL server (thường là 3306)
       - `VERIFY_DB_USER`: tên người dùng MySQL
       - `VERIFY_DB_PASSWORD`: mật khẩu MySQL
       - `VERIFY_DB_NAME`: tên database (Ông Ba Dạy Hoá website)
   - Bot sẽ tự động kết nối đến database khi khởi động

5. Khởi động bot ở chế độ development:
```bash
npm run dev
# Hoặc
yarn dev
```

## Cấu trúc Dự án

```
src/
├── features/                # Chứa code cho từng tính năng cụ thể
│   ├── [feature_name]/      # Thư mục cho một tính năng
│   │   ├── commands/        # Lệnh Discord liên quan đến tính năng
│   │   ├── services/        # Logic nghiệp vụ của tính năng
│   │   ├── models/          # (Tùy chọn) Định nghĩa model/schema dữ liệu
│   │   ├── events/          # (Tùy chọn) Xử lý sự kiện Discord
│   │   └── utils/           # (Tùy chọn) Các hàm tiện ích riêng
├── core/                    # Các thành phần cốt lõi của bot
├── shared/                  # Code được chia sẻ giữa nhiều tính năng
├── events/                  # Các event handler chung
├── commands/                # Các lệnh chung
└── index.js                 # Điểm vào chính của ứng dụng
```

## Phát triển

### Tạo tính năng mới

1. Tạo thư mục tính năng mới trong `src/features/`:
```bash
mkdir -p src/features/ten_tinh_nang/commands
mkdir -p src/features/ten_tinh_nang/services
# Thêm các thư mục khác nếu cần
```

2. Tạo README.md cho tính năng để mô tả chi tiết.

3. Phát triển các lệnh và logic nghiệp vụ cho tính năng.

4. Đăng ký lệnh bằng cách tạo các file trong thư mục `commands`.

### Quy tắc code

- Tuân thủ cấu trúc thư mục và quy ước đặt tên.
- Viết unit test cho các services và commands quan trọng.
- Tham khảo file `DEVELOPMENT_RULES.md` để biết thêm chi tiết về các nguyên tắc phát triển.

## Tính năng

### Verify (Xác thực học sinh)

Tính năng này cho phép học sinh xác thực tài khoản Discord của họ với thông tin đã mua khóa học trên website, và tự động gán các role tương ứng.

- **Hoạt động**: 
  - Admin tạo button xác thực trong kênh chỉ định bằng lệnh `/verify create-button`
  - Học sinh nhấn button và nhập email + mã kích hoạt đã nhận khi mua khóa học
  - Bot kiểm tra thông tin trong database và gán role tương ứng với khóa học
  
- **Yêu cầu database**: 
  - Cần quyền truy cập vào database của website Ông Ba Dạy Hoá
  - Các bảng cần thiết: `activation_codes`, `orders`, `orders_activation_codes_lnk`, `orders_users_permissions_user_lnk`, `up_users`, `orders_course_lnk`, `courses`, `orders_course_tier_lnk`, `course_tiers`
  - Cấu hình kết nối trong biến môi trường với tiền tố `VERIFY_DB_*`

## Triển khai (Deployment)

### Triển khai trên VPS

1. Cài đặt Node.js và MySQL trên server.
2. Clone repository và cài đặt dependencies.
3. Cấu hình biến môi trường với thông tin production.
4. Khởi động bot:
```bash
npm start
# Hoặc sử dụng process manager như PM2
pm2 start src/index.js --name ongbadayhoa-bot
```

## Lệnh có sẵn

- `/ping` - Kiểm tra độ trễ của bot và trạng thái hoạt động.
- (Các lệnh khác sẽ được thêm vào khi phát triển)

## Giấy phép

© 2025 Ông Ba Dạy Hoá - Mọi quyền được bảo lưu.