/**
 * @file jest.config.js
 * @description <PERSON><PERSON><PERSON> hình <PERSON> cho việc unit testing
 * <AUTHOR> Assistant
 * @date 2025-05-09
 */

module.exports = {
  // Thu thập coverage từ các file source
  collectCoverage: true,
  
  // Th<PERSON> mục đầu ra cho coverage reports
  coverageDirectory: 'coverage',
  
  // Các file nên thu thập coverage
  collectCoverageFrom: [
    'src/**/*.js',
    '!src/index.js',
    '!**/node_modules/**'
  ],
  
  // Môi trường test
  testEnvironment: 'node',
  
  // Mock tự động các module có thể có vấn đề khi test
  automock: false,
  
  // Các file hay module nên mock tự động
  moduleNameMapper: {
    '^src/(.*)$': '<rootDir>/src/$1'
  },
  
  // Các file setup trước khi chạy test
  setupFiles: [
    '<rootDir>/tests/setup.js'
  ],
  
  // Timeout cho mỗi test case (10 giây)
  testTimeout: 10000,
  
  // Không chạy các file trong node_modules
  transformIgnorePatterns: [
    '/node_modules/'
  ],
  
  // Hiển thị chi tiết khi test thất bại
  verbose: true
}; 