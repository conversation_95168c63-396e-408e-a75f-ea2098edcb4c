# Use Cases - <PERSON><PERSON> thống Điểm danh Livestream Discord

## 📋 Tổng quan Use Cases

Dưới đây là tất cả các tình huống có thể xảy ra trong hệ thống điểm danh livestream tự động, từ happy path đến edge cases phức tạp.

---

## 🎯 HAPPY PATH - Luồng Chuẩ<PERSON>

### UC001: <PERSON><PERSON><PERSON><PERSON> học diễn ra bình thường
**Actors:** <PERSON><PERSON> (Thầy Hóa), Students (<PERSON><PERSON><PERSON> sinh đã verified)

**Flow:**
1. **19:00** - Admin join voice channel `#livestream-hoa12`
2. Bot gửi message với button "🔴 Bắt đầu điểm danh" (chỉ admin thấy)
3. **19:05** - Admin click "Bắt đầu điểm danh"
4. **19:05-20:35** - 25 học sinh join/leave voice channel trong suốt buổi học
5. Bot real-time tracking attendance + chat messages
6. **20:35** - Admin click "⏹️ Kết thúc điểm danh"
7. Bot generate report → DM admin + post channel
8. **00:00** - Auto-delete chat messages

**Expected Result:**
- ✅ Session saved với 90 phút duration
- ✅ 18 học sinh >40 phút → "Có mặt"
- ✅ 7 học sinh <40 phút → "Vắng mặt"
- ✅ Chat messages saved + scheduled deletion
- ✅ Admin nhận báo cáo chi tiết

---

## 🔄 NORMAL VARIATIONS - Biến thể Bình thường

### UC002: Admin join sớm, start muộn
**Flow:**
1. **18:45** - Admin join voice channel (sớm 15 phút)
2. Bot gửi button "Bắt đầu điểm danh"
3. **19:00** - Admin mới click "Bắt đầu" (đúng giờ)
4. Session tracking bắt đầu từ 19:00, không tính thời gian 18:45-19:00

### UC003: Học sinh join trước khi admin start
**Flow:**
1. **18:55** - 5 học sinh đã join voice channel chờ
2. **19:00** - Admin join + start session
3. Bot retroactive tracking cho 5 học sinh từ thời điểm start (19:00)
4. Không tính thời gian họ chờ trước 19:00

### UC004: Admin quên click "Kết thúc"
**Flow:**
1. **20:35** - Buổi học kết thúc, admin leave voice channel
2. **20:40** - Admin nhận DM: "⚠️ Bạn có session đang active, click để kết thúc"
3. **20:45** - Admin click link trong DM → End session
4. **Alternative:** Auto-end sau 3 tiếng nếu admin không response

### UC005: Multiple students join/leave liên tục
**Flow:**
1. Học sinh A: Join 19:05 → Leave 19:30 → Join 19:45 → Leave 20:30
2. Bot tracking: 
   - `join_count: 2, leave_count: 2`
   - `total_presence_seconds: (25 phút + 45 phút) = 70 phút`
   - `attendance_percentage: 77.8%` → "Có mặt"

---

## ⚠️ EDGE CASES - Trường hợp Đặc biệt

### UC006: Admin bị disconnect giữa buổi học
**Scenario A: Admin reconnect nhanh (<5 phút)**
1. **19:30** - Admin bị rớt mạng, leave voice channel
2. Session vẫn tiếp tục tracking students
3. **19:33** - Admin rejoin voice channel
4. Bot gửi message: "🔄 Session đang active, click để kết thúc khi cần"

**Scenario B: Admin offline lâu (>10 phút)**
1. **19:30** - Admin disconnect
2. **19:40** - Bot auto-end session + gửi report
3. DM admin: "⚠️ Session đã tự động kết thúc do mất kết nối"

### UC007: Bot restart giữa buổi học
**Flow:**
1. **19:30** - Bot bị restart/crash
2. **19:32** - Bot khởi động lại
3. Bot check database → Phát hiện active session
4. Bot rejoin voice channel monitoring
5. Continue tracking từ thời điểm restart

### UC008: Duplicate session attempts
**Flow:**
1. **19:05** - Admin click "Bắt đầu điểm danh"
2. **19:10** - Admin click "Bắt đầu" lần nữa (nhầm lẫn)
3. Bot response: "⚠️ Session đã đang active từ 19:05"
4. Show current session status

### UC009: Wrong voice channel
**Flow:**
1. Admin join voice channel `#general-voice` (không phải livestream channel)
2. Bot không gửi button điểm danh
3. Admin phải move sang `#livestream-hoa12` mới có button

### UC010: No students join
**Flow:**
1. Admin start session bình thường
2. Không có học sinh nào join voice channel
3. Admin end session sau 90 phút
4. Report: "⚠️ Không có học sinh tham gia buổi học này"

---

## 🚨 ERROR CASES - Lỗi Hệ thống

### UC011: Database connection lost
**Flow:**
1. Session đang active, database bị disconnect
2. Bot cache attendance data in memory
3. Khi database reconnect → Bulk save cached data
4. Nếu bot restart trước khi save → Data loss, gửi warning cho admin

### UC012: Discord API rate limit
**Flow:**
1. Bot bị rate limit khi tracking nhiều join/leave events
2. Queue events in memory
3. Process events khi rate limit lifted
4. Log warning nếu có events bị drop

### UC013: Invalid user permissions
**Flow:**
1. Unverified user join voice channel
2. Bot skip tracking (chỉ track verified students)
3. Log event for audit: "Unverified user attempted join"

### UC014: Chat message too long
**Flow:**
1. Student gửi message >2000 characters
2. Bot truncate message: "Content too long... [truncated]"
3. Save truncated version to database

---

## 🔧 ADMIN MANAGEMENT CASES

### UC015: Multiple admins in channel
**Flow:**
1. Admin A start session
2. Admin B join voice channel
3. Admin B cũng thấy button "Kết thúc điểm danh"
4. Chỉ 1 admin click end → Session kết thúc cho tất cả

### UC016: Admin role changes mid-session
**Flow:**
1. Admin start session
2. Admin bị remove admin role (Discord permission change)
3. Session tiếp tục, nhưng admin không thể end session
4. Auto-end sau timeout hoặc backup admin end

### UC017: Backup admin scenario
**Flow:**
1. Main admin start session rồi offline
2. **19:45** - Backup admin join voice channel
3. Bot gửi message: "🔄 Phát hiện session active, bạn có muốn take control?"
4. Backup admin click → Có thể end session

---

## 📊 REPORTING CASES

### UC018: Perfect attendance
**Flow:**
1. Tất cả 30 học sinh enrolled join đủ >40 phút
2. Report: "🎉 Tỷ lệ tham gia: 100% (30/30)"
3. Không có absent list

### UC019: Low attendance
**Flow:**
1. Chỉ 10/30 học sinh tham gia đủ thời gian
2. Report: "⚠️ Tỷ lệ tham gia thấp: 33% (10/30)"
3. Detailed absent list với lý do (không join, join không đủ thời gian)

### UC020: Chat analysis
**Flow:**
1. Session có 150 chat messages
2. Report include: "💬 Tương tác chat: 150 tin nhắn từ 18 học sinh"
3. Top active chatters list

---

## 🕐 SCHEDULING CASES

### UC021: Daily cleanup
**Flow:**
1. **23:59** - Bot check tất cả active sessions
2. **00:00** - Auto-end sessions + delete chat messages
3. **00:01** - Send summary report cho admin về cleanup

### UC022: Weekend/Holiday
**Flow:**
1. Không có lịch học scheduled
2. Admin vẫn có thể manual start session
3. System hoạt động bình thường

### UC023: Timezone confusion
**Flow:**
1. Admin ở timezone khác start session
2. Bot sử dụng server timezone (UTC+7) cho tất cả timestamps
3. Report hiển thị đúng timezone Việt Nam

---

## 🔍 AUDIT & MONITORING CASES

### UC024: Suspicious activity
**Flow:**
1. Học sinh join/leave >20 lần trong 1 session
2. Bot flag: "⚠️ Unusual activity detected"
3. Include trong admin report để review

### UC025: Performance monitoring
**Flow:**
1. Session với >100 students
2. Bot monitor response time cho voice events
3. Log performance metrics
4. Alert nếu processing time >5 seconds

---

## 📝 Kết luận

**Tổng cộng: 25 Use Cases** covering:
- ✅ 5 Happy Path scenarios
- 🔄 5 Normal variations  
- ⚠️ 10 Edge cases
- 🚨 4 Error handling cases
- 🔧 3 Admin management cases
- 📊 3 Reporting scenarios
- 🕐 3 Scheduling cases
- 🔍 2 Monitoring cases


Hiện tại các use cases đã khá đầy đủ và chi tiết, bao quát được hầu hết các tình huống có thể xảy ra trong hệ thống điểm danh livestream.

Tôi nghĩ chúng ta đã sẵn sàng để chuyển sang bước tiếp theo - tạo task breakdown chi tiết để implement tính năng này.

# 📋 Task Breakdown - Hệ thống Điểm danh Livestream

## 1. Database Setup

- [ ] **Task 1.1: Thiết kế và tạo database schema**
  - Tạo bảng `livestream_sessions` (lưu thông tin buổi học)
  - Tạo bảng `attendance_records` (lưu thông tin điểm danh của học sinh)
  - Tạo bảng `attendance_join_leave_logs` (lưu chi tiết join/leave)
  - Tạo bảng `livestream_chat_messages` (lưu chat messages)

- [ ] **Task 1.2: Tạo database migration script**
  - Viết script để tạo các bảng mới
  - Đảm bảo backward compatibility
  - Thêm indexes cho performance

## 2. Core Feature Implementation

- [ ] **Task 2.1: Tạo module structure**
  - Tạo thư mục `src/features/livestream-attendance`
  - Setup file structure theo architecture hiện tại
  - Tạo README.md cho feature

- [ ] **Task 2.2: Implement Admin Control UI**
  - Tạo button "Bắt đầu điểm danh" khi admin join voice channel
  - Tạo button "Kết thúc điểm danh" khi session active
  - Implement permission check cho admin-only actions

- [ ] **Task 2.3: Implement Session Management**
  - Tạo service để start/end session
  - Lưu session metadata (admin, channel, start/end time)
  - Handle edge cases (admin disconnect, duplicate start)

- [ ] **Task 2.4: Implement Voice State Tracking**
  - Listen Discord voiceStateUpdate events
  - Track join/leave events cho mỗi user
  - Calculate total presence time

- [ ] **Task 2.5: Implement Chat Message Tracking**
  - Listen Discord messageCreate events trong voice channel
  - Store messages trong database
  - Setup scheduled job để delete messages lúc 12AM

## 3. Reporting & Analytics

- [ ] **Task 3.1: Implement Attendance Calculation**
  - Calculate attendance percentage cho mỗi học sinh
  - Apply threshold (>40 phút = có mặt)
  - Handle edge cases (multiple join/leave)

- [ ] **Task 3.2: Implement Report Generation**
  - Generate end-of-session report
  - Include attendance statistics, absent students
  - Format report với Discord embeds

- [ ] **Task 3.3: Implement Admin Notifications**
  - Send DM cho admin khi session ends
  - Send notification khi admin disconnect
  - Send daily summary (optional)

## 4. Error Handling & Edge Cases

- [ ] **Task 4.1: Implement Error Recovery**
  - Handle bot restart/crash during session
  - Implement in-memory caching khi database disconnect
  - Create recovery procedures

- [ ] **Task 4.2: Implement Timeout Handling**
  - Auto-end sessions sau timeout period
  - Handle admin không click "Kết thúc"
  - Send reminders to admin

- [ ] **Task 4.3: Implement Multiple Admin Support**
  - Allow multiple admins to control session
  - Implement backup admin functionality
  - Handle permission changes mid-session

## 5. Testing & Documentation

- [ ] **Task 5.1: Write Unit Tests**
  - Test attendance calculation logic
  - Test session management
  - Test error handling

- [ ] **Task 5.2: Write Integration Tests**
  - Test Discord event handling
  - Test database operations
  - Test report generation

- [ ] **Task 5.3: Create Documentation**
  - Update feature README.md
  - Document API và database schema
  - Create admin guide

## 6. Deployment & Monitoring

- [ ] **Task 6.1: Setup Monitoring**
  - Add logging cho attendance events
  - Track performance metrics
  - Setup alerts cho errors

- [ ] **Task 6.2: Prepare Deployment**
  - Create deployment script
  - Test in staging environment
  - Plan rollback strategy

---