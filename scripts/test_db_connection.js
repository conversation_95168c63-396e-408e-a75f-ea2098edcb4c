/**
 * @file test_db_connection.js
 * @description Script kiểm tra kết nối đến database và truy vấn chi tiết danh sách học sinh đã mua khóa học
 * <AUTHOR> Assistant
 * @date 2025-05-09
 */

// <PERSON>ad các cấu hình từ file .env
require('dotenv').config();

const mysql = require('mysql2/promise');
const util = require('util');

// Các thông tin kết nối từ biến môi trường
const dbConfig = {
  host: process.env.VERIFY_DB_HOST,
  port: process.env.VERIFY_DB_PORT,
  user: process.env.VERIFY_DB_USER,
  password: process.env.VERIFY_DB_PASSWORD,
  database: process.env.VERIFY_DB_NAME,
  waitForConnections: true,
  connectionLimit: 2,
  queueLimit: 0
};

async function testConnection() {
  console.log('Kết nối đến database với thông tin:');
  console.log(`- Host: ${dbConfig.host}:${dbConfig.port}`);
  console.log(`- Database: ${dbConfig.database}`);
  console.log(`- User: ${dbConfig.user}`);
  
  let connection;
  let pool;
  
  try {
    // Tạo pool kết nối
    pool = mysql.createPool(dbConfig);
    
    // Lấy một kết nối từ pool
    connection = await pool.getConnection();
    console.log('✅ Kết nối đến database thành công!');
    
    // Kiểm tra thông tin kết nối
    const [versionRows] = await connection.query('SELECT VERSION() as version');
    console.log(`MySQL Server phiên bản: ${versionRows[0].version}`);
    
    // 1. Kiểm tra tất cả các bảng trong database
    console.log('\n📋 Danh sách các bảng trong database:');
    const [tablesRows] = await connection.query('SHOW TABLES');
    console.log(tablesRows.map(row => Object.values(row)[0]).join(', '));
    
    // 2. Truy vấn chi tiết học sinh đã mua khóa học theo yêu cầu
    console.log('\n👨‍🎓 Danh sách chi tiết học sinh đã mua khóa học (giới hạn 5):');
    const [detailedRows] = await connection.query(`
      SELECT 
        u.id AS user_id,
        u.email,
        u.phone,
        u.fullname,
        u.gender,
        u.date,
        u.where_you_know_website,
        u.user_discord_id,
        ac.id AS activation_code_id,
        ac.code,
        ac.activated_at,
        ac.discord_status,
        ct.discord_role_id,
        c.title AS course_title,
        ct.tier_name,
        ct.tier_type,
        ct.price AS tier_price,
        o.id AS order_id,
        o.order_code AS order_code,
        o.payos_order_code,
        o.order_date,
        o.total_amount,
        o.delivery_address
      FROM orders o
      JOIN orders_users_permissions_user_lnk oupl ON o.id = oupl.order_id
      JOIN up_users u ON oupl.user_id = u.id
      JOIN orders_activation_codes_lnk oac ON o.id = oac.order_id
      JOIN activation_codes ac ON oac.activation_code_id = ac.id
      JOIN orders_course_lnk ocl ON o.id = ocl.order_id
      JOIN courses c ON ocl.course_id = c.id
      JOIN orders_course_tier_lnk octl ON o.id = octl.order_id
      JOIN course_tiers ct ON octl.course_tier_id = ct.id
      WHERE o.payment_status = 'completed'
      LIMIT 5
    `);
    
    // In kết quả chi tiết nhưng không hiển thị thông tin nhạy cảm
    console.log('Tìm thấy ' + detailedRows.length + ' học sinh:');
    detailedRows.forEach((student, index) => {
      console.log(`\n--- Học sinh ${index + 1} ---`);
      console.log(`- Họ tên: ${student.fullname}`);
      console.log(`- Email: ${student.email}`);
      console.log(`- Khóa học: ${student.course_title} (${student.tier_name || 'Standard'})`);
      console.log(`- Mã kích hoạt: ${student.code}`);
      console.log(`- Trạng thái Discord: ${student.user_discord_id ? 'Đã liên kết' : 'Chưa liên kết'}`);
      console.log(`- Trạng thái kích hoạt: ${student.activated_at ? 'Đã kích hoạt' : 'Chưa kích hoạt'}`);
      console.log(`- Discord Role ID: ${student.discord_role_id || 'Chưa thiết lập'}`);
      console.log(`- Mã đơn hàng: ${student.order_code}`);
      console.log(`- Ngày đặt hàng: ${student.order_date}`);
      console.log(`- Tier type: ${student.tier_type || 'N/A'}`);
    });
    
    // 3. Lưu chi tiết vào file để tiện tham khảo (không ghi thông tin nhạy cảm)
    console.log('\n📝 Chi tiết đầy đủ:');
    const sanitizedData = detailedRows.map(row => {
      // Loại bỏ hoặc che dấu thông tin nhạy cảm
      const clone = {...row};
      if (clone.phone) clone.phone = '********' + (clone.phone.slice(-3) || '');
      if (clone.delivery_address) clone.delivery_address = '[hidden]';
      return clone;
    });
    console.log(util.inspect(sanitizedData, { colors: true, depth: 4, maxArrayLength: 5 }));
    
    // 4. Đếm tổng số học sinh đã mua khóa học
    const [countRows] = await connection.query(`
      SELECT COUNT(DISTINCT u.id) AS total_students
      FROM orders o
      JOIN orders_users_permissions_user_lnk oupl ON o.id = oupl.order_id
      JOIN up_users u ON oupl.user_id = u.id
      WHERE o.payment_status = 'completed'
    `);
    console.log(`\n📊 Tổng số học sinh đã mua khóa học: ${countRows[0].total_students}`);
    
    // 5. Thống kê các khóa học theo số lượng học sinh đã mua
    console.log('\n📚 Thống kê khóa học theo số lượng học sinh:');
    const [courseStats] = await connection.query(`
      SELECT c.title AS course_title, COUNT(DISTINCT u.id) AS student_count
      FROM courses c
      JOIN orders_course_lnk ocl ON c.id = ocl.course_id
      JOIN orders o ON ocl.order_id = o.id
      JOIN orders_users_permissions_user_lnk oupl ON o.id = oupl.order_id
      JOIN up_users u ON oupl.user_id = u.id
      WHERE o.payment_status = 'completed'
      GROUP BY c.id
      ORDER BY student_count DESC
    `);
    console.log(util.inspect(courseStats, { colors: true, depth: 2 }));
    
    // 6. Kiểm tra tier có Discord Role ID
    console.log('\n👑 Các Tier có Discord Role ID:');
    const [tierWithRoles] = await connection.query(`
      SELECT ct.id, ct.tier_name, ct.discord_role_id, c.title AS course_title, ct.tier_type
      FROM course_tiers ct
      JOIN course_tiers_course_lnk ctl ON ct.id = ctl.course_tier_id
      JOIN courses c ON ctl.course_id = c.id
      WHERE ct.discord_role_id IS NOT NULL AND ct.discord_role_id != ''
    `);
    console.log(util.inspect(tierWithRoles, { colors: true, depth: 3 }));
    
    console.log('\n✅ Kiểm tra kết nối và truy vấn dữ liệu thành công!');
    
  } catch (error) {
    console.error('❌ Lỗi khi kết nối hoặc truy vấn database:');
    console.error(error);
  } finally {
    // Đóng kết nối và pool
    if (connection) connection.release();
    if (pool) await pool.end();
    console.log('\n🔌 Đã đóng kết nối database.');
  }
}

// Thực thi hàm kiểm tra
testConnection().catch(console.error); 