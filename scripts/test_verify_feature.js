/**
 * @file test_verify_feature.js
 * @description Script kiểm tra tính năng xác thực học sinh mà không thực hiện thay đổi database
 * <AUTHOR> Assistant
 * @date 2025-05-09
 */

// Load các cấu hình từ file .env
require('dotenv').config();

const mysql = require('mysql2/promise');
const util = require('util');

// C<PERSON><PERSON> thông tin kết nối từ biến môi trường
const dbConfig = {
  host: process.env.VERIFY_DB_HOST,
  port: process.env.VERIFY_DB_PORT,
  user: process.env.VERIFY_DB_USER,
  password: process.env.VERIFY_DB_PASSWORD,
  database: process.env.VERIFY_DB_NAME,
  waitForConnections: true,
  connectionLimit: 2,
  queueLimit: 0
};

// Các testcase đầu vào để kiểm tra
const TEST_CASES = [
  {
    email: '<EMAIL>',
    activationCode: '836GOEU',
    discordUserId: '123456789012345678', // Discord ID giả định cho mục đích test
    description: 'Học sinh có mã hợp lệ (chưa kích hoạt)'
  },
  {
    email: '<EMAIL>',
    activationCode: 'INVALID', // Mã không tồn tại
    discordUserId: '123456789012345678',
    description: 'Học sinh với mã kích hoạt không hợp lệ'
  },
  {
    email: '<EMAIL>', // Email không tồn tại
    activationCode: '836GOEU',
    discordUserId: '123456789012345678',
    description: 'Email không tồn tại trong hệ thống'
  }
];

/**
 * Mô phỏng quá trình xác thực của một học sinh
 * @param {Object} connection Database connection
 * @param {Object} testCase Dữ liệu test case
 */
async function simulateVerification(connection, testCase) {
  console.log(`\n\n=== Testing: ${testCase.description} ===`);
  console.log(`- Email: ${testCase.email}`);
  console.log(`- Mã kích hoạt: ${testCase.activationCode}`);
  console.log(`- Discord User ID: ${testCase.discordUserId}`);
  console.log('-----------------------------------');

  try {
    // Bước 1: Kiểm tra email và mã kích hoạt có tồn tại
    const query = `
      SELECT 
        u.id AS user_id,
        u.email,
        u.fullname,
        ac.id AS activation_code_id,
        ac.code,
        ac.activated_at,
        ac.discord_status,
        ct.discord_role_id,
        c.title AS course_title,
        ct.tier_name,
        o.id AS order_id,
        o.payment_status
      FROM activation_codes ac
      JOIN orders_activation_codes_lnk oac ON ac.id = oac.activation_code_id
      JOIN orders o ON oac.order_id = o.id
      JOIN orders_users_permissions_user_lnk oupl ON o.id = oupl.order_id
      JOIN up_users u ON oupl.user_id = u.id
      JOIN orders_course_tier_lnk octl ON o.id = octl.order_id
      JOIN course_tiers ct ON octl.course_tier_id = ct.id
      JOIN orders_course_lnk ocl ON o.id = ocl.order_id
      JOIN courses c ON ocl.course_id = c.id
      WHERE u.email = ? AND ac.code = ? AND o.payment_status = 'completed'
      LIMIT 1
    `;
    
    const [verificationRows] = await connection.query(query, [testCase.email, testCase.activationCode]);
    
    if (verificationRows.length === 0) {
      console.log('❌ Xác thực thất bại: Email hoặc mã kích hoạt không hợp lệ hoặc đơn hàng chưa hoàn tất thanh toán');
      return false;
    }
    
    const verificationInfo = verificationRows[0];
    console.log('✅ Thông tin tìm thấy:');
    console.log(`- Họ tên: ${verificationInfo.fullname}`);
    console.log(`- Khóa học: ${verificationInfo.course_title} (${verificationInfo.tier_name})`);
    console.log(`- Discord Role ID: ${verificationInfo.discord_role_id || 'Chưa thiết lập'}`);
    
    // Bước 2: Kiểm tra trạng thái kích hoạt
    if (verificationInfo.activated_at || verificationInfo.discord_status === '1') {
      console.log('❌ Xác thực thất bại: Mã kích hoạt đã được sử dụng');
      return false;
    }
    
    // Bước 3: Kiểm tra Discord Role ID
    if (!verificationInfo.discord_role_id) {
      console.log('⚠️ Cảnh báo: Tier này chưa thiết lập Discord Role ID!');
    }
    
    // Bước 4: Xác thực thành công - Mô phỏng cập nhật dữ liệu
    console.log('\n✅ XÁC THỰC THÀNH CÔNG! Các thay đổi sẽ được thực hiện (đây chỉ là mô phỏng):');
    console.log(`1. Cập nhật activation_codes.discord_status = '1' cho mã ${verificationInfo.code}`);
    console.log(`2. Cập nhật up_users.user_discord_id = '${testCase.discordUserId}' cho user ${verificationInfo.user_id}`);
    console.log(`3. Cấp Discord Role ID: ${verificationInfo.discord_role_id || 'Không có role để cấp'}`);
    
    return {
      success: true,
      user: {
        id: verificationInfo.user_id,
        fullname: verificationInfo.fullname,
        email: verificationInfo.email
      },
      course: {
        title: verificationInfo.course_title,
        tier: verificationInfo.tier_name
      },
      discordRoleId: verificationInfo.discord_role_id
    };
    
  } catch (error) {
    console.error('❌ Lỗi trong quá trình xác thực:', error.message);
    return false;
  }
}

/**
 * Chạy các test case trên database
 */
async function testVerifyFeature() {
  console.log('🔍 Bắt đầu kiểm tra tính năng verify...');
  console.log(`Kết nối đến database: ${dbConfig.host}:${dbConfig.port} | DB: ${dbConfig.database}`);
  
  let connection;
  let pool;
  
  try {
    // Tạo pool kết nối
    pool = mysql.createPool(dbConfig);
    
    // Lấy một kết nối từ pool
    connection = await pool.getConnection();
    console.log('✅ Kết nối đến database thành công!\n');
    
    // Thực hiện các test case
    const results = [];
    for (const testCase of TEST_CASES) {
      const result = await simulateVerification(connection, testCase);
      results.push({
        description: testCase.description,
        result: result
      });
    }
    
    // Tổng kết kết quả
    console.log('\n\n====== TÓM TẮT KẾT QUẢ ======');
    results.forEach((item, index) => {
      console.log(`${index + 1}. ${item.description}:`);
      console.log(`   ${item.result ? '✅ Thành công' : '❌ Thất bại'}`);
    });
    
  } catch (error) {
    console.error('❌ Lỗi khi kết nối hoặc truy vấn database:', error);
  } finally {
    // Đóng kết nối và pool
    if (connection) connection.release();
    if (pool) await pool.end();
    console.log('\n🔌 Đã đóng kết nối database.');
  }
}

// Thực thi hàm kiểm tra
testVerifyFeature().catch(console.error); 