/**
 * @file course.js
 * @description Lệnh quản lý kh<PERSON><PERSON> họ<PERSON>, cho phép tạo và xóa cấu trúc kênh cho khóa học
 * <AUTHOR> Assistant
 * @date 2023-05-13
 * @version 1.0.0
 * @changelog
 * - 1.0.0: <PERSON><PERSON><PERSON> bả<PERSON> ban đầu, hỗ trợ tạo và xóa khóa học
 */

const { SlashCommandBuilder, PermissionFlagsBits, ChannelType, MessageFlags, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const logger = require('../../shared/utils/logger');
const config = require('../../core/config'); // Import config

// Quyền mặc định cho mọi kênh
const DEFAULT_PERMISSIONS = {
    // Chặn tất cả quyền mặc định
    [PermissionFlagsBits.ViewChannel]: false,
    [PermissionFlagsBits.CreateInstantInvite]: false,
    [PermissionFlagsBits.KickMembers]: false,
    [PermissionFlagsBits.BanMembers]: false,
    [PermissionFlagsBits.Administrator]: false,
    [PermissionFlagsBits.ManageChannels]: false,
    [PermissionFlagsBits.ManageGuild]: false,
    [PermissionFlagsBits.AddReactions]: true, // Cho phép thả reaction
    [PermissionFlagsBits.ViewAuditLog]: false,
    [PermissionFlagsBits.PrioritySpeaker]: false,
    [PermissionFlagsBits.Stream]: false,
    [PermissionFlagsBits.SendMessages]: false,
    [PermissionFlagsBits.SendTTSMessages]: false,
    [PermissionFlagsBits.ManageMessages]: false,
    [PermissionFlagsBits.EmbedLinks]: false,
    [PermissionFlagsBits.AttachFiles]: false,
    [PermissionFlagsBits.ReadMessageHistory]: true,
    [PermissionFlagsBits.MentionEveryone]: false,
    [PermissionFlagsBits.UseExternalEmojis]: true,
    [PermissionFlagsBits.ViewGuildInsights]: false,
    [PermissionFlagsBits.Connect]: false,
    [PermissionFlagsBits.Speak]: false,
    [PermissionFlagsBits.MuteMembers]: false,
    [PermissionFlagsBits.DeafenMembers]: false,
    [PermissionFlagsBits.MoveMembers]: false,
    [PermissionFlagsBits.UseVAD]: false,
    [PermissionFlagsBits.ChangeNickname]: false,
    [PermissionFlagsBits.ManageNicknames]: false,
    [PermissionFlagsBits.ManageRoles]: false,
    [PermissionFlagsBits.ManageWebhooks]: false,
    [PermissionFlagsBits.ManageGuildExpressions]: false, // Thay thế ManageEmojisAndStickers đã bị deprecated
    [PermissionFlagsBits.UseApplicationCommands]: true,
    [PermissionFlagsBits.RequestToSpeak]: false,
    [PermissionFlagsBits.ManageEvents]: false,
    [PermissionFlagsBits.ManageThreads]: false,
    [PermissionFlagsBits.CreatePublicThreads]: false,
    [PermissionFlagsBits.CreatePrivateThreads]: false,
    [PermissionFlagsBits.UseExternalStickers]: true,
    [PermissionFlagsBits.SendMessagesInThreads]: false,
    [PermissionFlagsBits.UseEmbeddedActivities]: false,
    [PermissionFlagsBits.ModerateMembers]: false
};

const COURSE_CHANNELS = [
    {
        name: '👋-giới-thiệu',
        type: ChannelType.GuildText,
        permissions: {
            ...DEFAULT_PERMISSIONS,
            [PermissionFlagsBits.ViewChannel]: true,
            [PermissionFlagsBits.ReadMessageHistory]: true,
            [PermissionFlagsBits.SendMessages]: false // Chỉ đọc
        }
    },
    {
        name: '🗓️-lịch-học',
        type: ChannelType.GuildText,
        permissions: {
            ...DEFAULT_PERMISSIONS,
            [PermissionFlagsBits.ViewChannel]: true,
            [PermissionFlagsBits.ReadMessageHistory]: true
        }
    },
    {
        name: '📢-thông-báo',
        type: ChannelType.GuildText,
        permissions: {
            ...DEFAULT_PERMISSIONS,
            [PermissionFlagsBits.ViewChannel]: true,
            [PermissionFlagsBits.ReadMessageHistory]: true
        }
    },
    {
        name: '📚-tài-liệu',
        type: ChannelType.GuildText,
        permissions: {
            ...DEFAULT_PERMISSIONS,
            [PermissionFlagsBits.ViewChannel]: true,
            [PermissionFlagsBits.ReadMessageHistory]: true
        }
    },
    {
        name: '❓-hỏi-đáp',
        type: ChannelType.GuildText,
        permissions: {
            ...DEFAULT_PERMISSIONS,
            [PermissionFlagsBits.ViewChannel]: true,
            [PermissionFlagsBits.SendMessages]: true,
            [PermissionFlagsBits.AttachFiles]: true,
            [PermissionFlagsBits.EmbedLinks]: true,
            [PermissionFlagsBits.ReadMessageHistory]: true
        }
    },
    {
        name: '📼-xem-lại-stream',
        type: ChannelType.GuildText,
        permissions: {
            ...DEFAULT_PERMISSIONS,
            [PermissionFlagsBits.ViewChannel]: true,
            [PermissionFlagsBits.ReadMessageHistory]: true
        }
    },
    {
        name: '🎥-livestream',
        type: ChannelType.GuildVoice,
        permissions: {
            ...DEFAULT_PERMISSIONS,
            [PermissionFlagsBits.ViewChannel]: true,
            [PermissionFlagsBits.Connect]: true,
            [PermissionFlagsBits.Speak]: false,
            [PermissionFlagsBits.Stream]: false,
            [PermissionFlagsBits.SendMessages]: true,
            [PermissionFlagsBits.AttachFiles]: true,
            [PermissionFlagsBits.ReadMessageHistory]: true
        }
    }
];

// Cập nhật phần tạo channel trong hàm execute:
const createChannel = async (interaction, channelConfig, category, studentRole) => {
    const channel = await interaction.guild.channels.create({
        name: channelConfig.name,
        type: channelConfig.type,
        parent: category.id,
        permissionOverwrites: [
            {
                id: interaction.guild.id, // @everyone
                deny: Object.entries(DEFAULT_PERMISSIONS)
                    .filter(([_, value]) => !value) // Lấy các quyền bị DENY trong DEFAULT_PERMISSIONS
                    .map(([perm]) => perm)
                    .concat([PermissionFlagsBits.ViewChannel]) // Chặn @everyone xem kênh
            },
            {
                id: studentRole.id,
                allow: Object.entries(channelConfig.permissions)
                    .filter(([_, value]) => value)
                    .map(([perm]) => perm),
                deny: Object.entries(channelConfig.permissions)
                    .filter(([_, value]) => !value)
                    .map(([perm]) => perm)
            }
        ]
    });
    return channel;
};

module.exports = {
    data: new SlashCommandBuilder()
        .setName('course')
        .setDescription('Quản lý khóa học')
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator)
        .addSubcommand(subcommand =>
            subcommand
                .setName('create')
                .setDescription('Tạo cấu trúc mới cho một khóa học')
                .addStringOption(option =>
                    option
                        .setName('name')
                        .setDescription('Tên khóa học')
                        .setRequired(true))
                .addRoleOption(option =>
                    option
                        .setName('student_role')
                        .setDescription('Role cho học viên')
                        .setRequired(true)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('delete')
                .setDescription('Xóa một khóa học')
                .addStringOption(option =>
                    option
                        .setName('category_id')
                        .setDescription('ID của category khóa học')
                        .setRequired(true))),

    async execute(interaction) {
        // Kiểm tra quyền admin đặc biệt
        if (interaction.user.id !== config.permissions.verifyAdminId) {
            logger.warn(`Người dùng ${interaction.user.tag} (ID: ${interaction.user.id}) không có quyền sử dụng lệnh /course.`);
            try {
                await interaction.reply({ 
                    content: '❌ Bạn không có quyền sử dụng lệnh này.', 
                    flags: MessageFlags.Ephemeral 
                });
            } catch (e) {
                logger.error('Không thể gửi tin nhắn từ chối quyền cho lệnh course:', { message: e.message, stack: e.stack });
            }
            return;
        }

        try {
            const subcommand = interaction.options.getSubcommand();
            logger.debug(`Thực thi lệnh course với subcommand: ${subcommand}`);

            if (subcommand === 'create') {
                const courseName = interaction.options.getString('name');
                const studentRole = interaction.options.getRole('student_role');

                logger.debug(`Tạo khóa học mới: ${courseName} với role: ${studentRole.name}`);

                try {
                    await interaction.deferReply({ flags: MessageFlags.Ephemeral });
                } catch (error) {
                    if (error.code === 10062) return; // Bỏ qua lỗi Unknown interaction
                    throw error;
                }

                try {
                    // Tạo category với quyền private
                    const category = await interaction.guild.channels.create({
                        name: `📚 ${courseName}`,
                        type: ChannelType.GuildCategory,
                        permissionOverwrites: [
                            {
                                id: interaction.guild.id, // @everyone
                                deny: [
                                    PermissionFlagsBits.ViewChannel,
                                    PermissionFlagsBits.Connect,
                                    PermissionFlagsBits.SendMessages,
                                    PermissionFlagsBits.AddReactions,
                                    PermissionFlagsBits.CreateInstantInvite,
                                    PermissionFlagsBits.UseExternalEmojis,
                                    PermissionFlagsBits.UseExternalStickers,
                                    PermissionFlagsBits.UseApplicationCommands
                                ]
                            },
                            {
                                id: studentRole.id,
                                allow: [
                                    PermissionFlagsBits.ViewChannel,
                                    PermissionFlagsBits.ReadMessageHistory,
                                    PermissionFlagsBits.AddReactions,
                                    PermissionFlagsBits.UseExternalEmojis,
                                    PermissionFlagsBits.UseExternalStickers,
                                    PermissionFlagsBits.UseApplicationCommands,
                                    PermissionFlagsBits.Connect // Cho phép connect vào voice channels trong category
                                ],
                                deny: [
                                    PermissionFlagsBits.CreateInstantInvite,
                                    PermissionFlagsBits.ManageChannels,
                                    PermissionFlagsBits.ManageRoles,
                                    PermissionFlagsBits.ManageWebhooks,
                                    PermissionFlagsBits.ManageMessages,
                                    // Quyền Speak và SendMessages sẽ được quản lý ở cấp channel
                                ]
                            }
                        ]
                    });

                    logger.debug(`Đã tạo category: ${category.name} (${category.id})`);

                    // Tạo các kênh chung
                    const commonChannels = await Promise.all(COURSE_CHANNELS.map(channelConfig =>
                        createChannel(interaction, channelConfig, category, studentRole)
                    ));

                    // Tìm kênh giới thiệu để gửi embed
                    const introductionChannel = commonChannels.find(ch => ch.name.includes('giới-thiệu'));

                    if (introductionChannel) {
                        const welcomeEmbed = new EmbedBuilder()
                            .setColor('#0099ff')
                            .setTitle(`👋 Chào mừng bạn đến với Khóa học: ${courseName}!`)
                            .setDescription('Hãy sử dụng các nút bên dưới để nhanh chóng di chuyển đến các kênh trong khóa học.')
                            .setTimestamp();

                        const channelsToDisplay = commonChannels.filter(ch => ch.id !== introductionChannel.id);
                        const components = [];
                        let currentActionRow = new ActionRowBuilder();

                        channelsToDisplay.forEach((channel, index) => {
                            const channelNameForButton = channel.name.substring(channel.name.indexOf('-') + 1).replace(/-/g, ' ');
                            const emojiForButton = channel.name.split('-')[0];

                            currentActionRow.addComponents(
                                new ButtonBuilder()
                                    .setLabel(`Truy cập ${channelNameForButton}`)
                                    .setStyle(ButtonStyle.Link)
                                    .setURL(`https://discord.com/channels/${interaction.guild.id}/${channel.id}`)
                                    .setEmoji(emojiForButton)
                            );

                            // Sau mỗi 3 button hoặc là button cuối cùng, thêm ActionRow vào components và reset currentActionRow
                            // Hoặc nếu chỉ có 1-2 button ở hàng cuối cùng
                            if ((index + 1) % 3 === 0 || index === channelsToDisplay.length - 1) {
                                components.push(currentActionRow);
                                if (index < channelsToDisplay.length - 1) { // Chỉ tạo mới nếu không phải là cái cuối cùng đã được thêm
                                    currentActionRow = new ActionRowBuilder();
                                }
                            }
                        });
                        
                        try {
                            await introductionChannel.send({ embeds: [welcomeEmbed], components: components });
                            logger.debug(`Đã gửi tin nhắn giới thiệu với buttons vào kênh ${introductionChannel.name}`);
                        } catch (sendError) {
                            logger.error(`Không thể gửi tin nhắn embed với buttons vào kênh giới thiệu: ${sendError.message}`, { stack: sendError.stack });
                        }
                    }

                    logger.info(`Đã tạo thành công khóa học ${courseName} với ${commonChannels.length} kênh`);

                    await interaction.editReply({
                        content: `✅ Đã tạo thành công khóa học ${courseName}!
` +
                                `Category ID: ${category.id}
` +
                                `Số kênh đã tạo: ${commonChannels.length}`
                    });
                } catch (error) {
                    logger.error('Lỗi khi tạo khóa học:', {
                        message: error.message,
                        stack: error.stack,
                        courseName,
                        guildId: interaction.guild.id
                    });

                    try {
                        await interaction.editReply({
                            content: `❌ Có lỗi xảy ra khi tạo khóa học: ${error.message}`
                        });
                    } catch (e) {
                        if (e.code === 10008) { // DiscordAPIErrorCodes.UnknownMessage
                            logger.warn(`Không thể editReply sau khi tạo khóa học (có thể interaction đã hết hạn hoặc tin nhắn bị dismiss): ${e.message}`);
                        } else if (e.code !== 10062 && e.code !== 40060) { // Unknown Interaction, Cannot send messages to this user
                            logger.error('Không thể gửi thông báo lỗi khi tạo khóa học:', {
                                message: e.message,
                                stack: e.stack
                            });
                        }
                    }
                }

            } else if (subcommand === 'delete') {
                try {
                    await interaction.deferReply({ flags: MessageFlags.Ephemeral });
                } catch (error) {
                    if (error.code === 10062) return; // Bỏ qua lỗi Unknown interaction
                    throw error;
                }

                try {
                    const categoryId = interaction.options.getString('category_id');
                    logger.debug(`Xóa khóa học với category ID: ${categoryId}`);

                    const category = await interaction.guild.channels.fetch(categoryId);

                    if (!category) {
                        logger.warn(`Không tìm thấy category với ID: ${categoryId}`);
                        return await interaction.editReply({
                            content: '❌ Không tìm thấy category này!'
                        });
                    }

                    if (category.type !== ChannelType.GuildCategory) {
                        logger.warn(`ID ${categoryId} không phải là một category`);
                        return await interaction.editReply({
                            content: '❌ ID này không phải là một category!'
                        });
                    }

                    // Lấy danh sách tất cả các kênh trong category
                    const channels = category.children.cache;
                    logger.debug(`Tìm thấy ${channels.size} kênh trong category ${category.name}`);

                    // Xóa từng kênh một
                    for (const channel of channels.values()) {
                        await channel.delete()
                            .catch(error => logger.error(`Không thể xóa kênh ${channel.name}:`, {
                                message: error.message,
                                stack: error.stack
                            }));
                    }

                    // Sau khi xóa hết các kênh, xóa category
                    await category.delete();
                    logger.info(`Đã xóa thành công category ${category.name} và ${channels.size} kênh`);

                    await interaction.editReply({
                        content: `✅ Đã xóa thành công khóa học và ${channels.size} kênh!`
                    });
                } catch (error) {
                    logger.error('Lỗi khi xóa khóa học:', {
                        message: error.message,
                        stack: error.stack,
                        categoryId: interaction.options.getString('category_id'),
                        guildId: interaction.guild.id
                    });

                    try {
                        await interaction.editReply({
                            content: `❌ Có lỗi xảy ra khi xóa khóa học: ${error.message}`
                        });
                    } catch (e) {
                        if (e.code === 10008) { // DiscordAPIErrorCodes.UnknownMessage
                            logger.warn(`Không thể editReply sau khi xóa khóa học (có thể interaction đã hết hạn hoặc tin nhắn bị dismiss): ${e.message}`);
                        } else if (e.code !== 10062 && e.code !== 40060) { // Unknown Interaction, Cannot send messages to this user
                            logger.error('Không thể gửi thông báo lỗi khi xóa khóa học:', {
                                message: e.message,
                                stack: e.stack
                            });
                        }
                    }
                }
            }
        } catch (error) {
            logger.error('Lỗi trong lệnh course:', {
                message: error.message,
                stack: error.stack,
                subcommand: interaction.options?.getSubcommand(),
                guildId: interaction.guild?.id
            });

            if (error.code === 10062) return; // Bỏ qua lỗi Unknown interaction

            try {
                const errorMessage = `❌ Có lỗi xảy ra: ${error.message}`;
                if (!interaction.replied && !interaction.deferred) {
                    // Nếu chưa trả lời hoặc defer, thử reply
                    await interaction.reply({ content: errorMessage, flags: MessageFlags.Ephemeral });
                } else {
                    // Nếu đã defer hoặc reply, thử editReply
                    await interaction.editReply({ content: errorMessage });
                }
            } catch (e) {
                if (e.code === 10008) { // DiscordAPIErrorCodes.UnknownMessage
                    logger.warn(`Không thể gửi/edit thông báo lỗi chính (có thể interaction đã hết hạn hoặc tin nhắn bị dismiss): ${e.message}`);
                } else if (e.code !== 10062 && e.code !== 40060) { // Unknown Interaction, Cannot send messages to this user
                    logger.error('Không thể gửi thông báo lỗi cuối cùng:', {
                        message: e.message,
                        stack: e.stack
                    });
                }
            }
        }
    }
};
