/**
 * @file ping.js
 * @description Lệnh ping đơn giản để kiểm tra bot hoạt động
 * <AUTHOR> Assistant
 * @date 2025-05-19
 */

const { SlashCommandBuilder } = require('discord.js');
const logger = require('../../shared/utils/logger');
const config = require('../../core/config'); // Import config

module.exports = {
  // Định nghĩa command
  data: new SlashCommandBuilder()
    .setName('ping')
    .setDescription('Kiểm tra độ trễ của bot và trạng thái hoạt động'),
  
  // <PERSON>àm thực thi khi command được gọi
  async execute(interaction) {
    try {
      // Gửi phản hồi ban đầu
      await interaction.deferReply();
      
      // Tính toán độ trễ
      const sent = await interaction.followUp({ content: '<PERSON><PERSON> tính toán...' });
      const timeDiff = Math.abs(sent.createdTimestamp - interaction.createdTimestamp);
      
      // Độ trễ API của Discord
      const apiLatency = Math.round(interaction.client.ws.ping);
      
      // Phản hồi với thông tin độ trễ
      await interaction.editReply({
        content: null,
        embeds: [{
          title: '🏓 Pong!',
          description: `Bot đang hoạt động!`,
          fields: [
            {
              name: '⏱️ Độ trễ Bot',
              value: `${timeDiff}ms`,
              inline: true
            },
            {
              name: '⌛ Độ trễ API',
              value: `${apiLatency}ms`,
              inline: true
            }
          ],
          color: 0x3498DB, // Màu xanh
          timestamp: new Date()
        }]
      });
      
      logger.debug(`Command ping thực thi thành công, độ trễ: ${timeDiff}ms, API: ${apiLatency}ms`);
    } catch (error) {
      logger.error('Lỗi khi thực thi lệnh ping:', { 
        message: error.message, 
        stack: error.stack 
      });
      
      // Gửi thông báo lỗi cho người dùng
      if (interaction.deferred || interaction.replied) {
        await interaction.followUp({ 
          content: 'Có lỗi xảy ra khi thực hiện lệnh.', 
          ephemeral: true 
        });
      } else {
        await interaction.reply({ 
          content: 'Có lỗi xảy ra khi thực hiện lệnh.', 
          ephemeral: true 
        });
      }
    }
  }
}; 