/**
 * @file bot.js
 * @description Module khởi tạo bot và quản lý các commands và events
 * <AUTHOR> Assistant
 * @date 2025-05-19
 */

const fs = require('fs');
const path = require('path');
const { REST } = require('@discordjs/rest');
const { Routes } = require('discord-api-types/v10');
const { Collection } = require('discord.js');
const config = require('./config');
const logger = require('../shared/utils/logger');

/**
 * Đăng ký các slash commands với Discord API
 * @param {Array} commands - <PERSON><PERSON><PERSON> commands cần đăng ký
 * @returns {Promise<void>}
 */
async function registerCommands(commands) {
  try {
    logger.info(`Bắt đầu đăng ký ${commands.length} slash commands với Discord API`);
    
    const rest = new REST({ version: '10' }).setToken(config.bot.token);
    
    if (config.app.nodeEnv === 'production') {
      // <PERSON><PERSON><PERSON> ký commands global trong production
      await rest.put(
        Routes.applicationCommands(config.bot.clientId),
        { body: commands }
      );
      logger.info('Đăng ký global slash commands thành công');
    } else {
      // Đăng ký commands cho guild cụ thể trong development
      await rest.put(
        Routes.applicationGuildCommands(config.bot.clientId, config.bot.guildId),
        { body: commands }
      );
      logger.info(`Đăng ký slash commands cho guild ${config.bot.guildId} thành công`);
    }
  } catch (error) {
    logger.error('Lỗi khi đăng ký slash commands:', { 
      message: error.message,
      stack: error.stack
    });
    throw error;
  }
}

/**
 * Load tất cả các tệp commands từ thư mục features và thư mục commands
 * @param {Client} client - Discord client instance
 * @returns {Array} Mảng các command data để đăng ký với Discord API
 */
function loadCommands(client) {
  try {
    // Đảm bảo collection commands đã được khởi tạo
    if (!client.commands) {
      client.commands = new Collection();
    }
    
    const commandsToRegister = [];
    
    // Load commands từ thư mục commands
    const commandsPath = path.join(__dirname, '..', 'commands');
    if (fs.existsSync(commandsPath)) {
      loadCommandsFromDirectory(commandsPath, client, commandsToRegister);
    }
    
    // Load commands từ các thư mục features
    const featuresPath = path.join(__dirname, '..', 'features');
    const features = fs.readdirSync(featuresPath, { withFileTypes: true })
      .filter(dirent => dirent.isDirectory())
      .map(dirent => dirent.name);
    
    for (const feature of features) {
      const featureCommandsPath = path.join(featuresPath, feature, 'commands');
      if (fs.existsSync(featureCommandsPath)) {
        loadCommandsFromDirectory(featureCommandsPath, client, commandsToRegister);
      }
    }
    
    logger.info(`Đã load ${client.commands.size} commands thành công`);
    return commandsToRegister;
  } catch (error) {
    logger.error('Lỗi khi load commands:', { 
      message: error.message,
      stack: error.stack
    });
    throw error;
  }
}

/**
 * Load các command files từ một thư mục cụ thể
 * @param {string} directory - Đường dẫn thư mục
 * @param {Client} client - Discord client instance
 * @param {Array} commandsToRegister - Mảng lưu các command data để đăng ký
 */
function loadCommandsFromDirectory(directory, client, commandsToRegister) {
  const items = fs.readdirSync(directory, { withFileTypes: true });
    
  for (const item of items) {
    const itemPath = path.join(directory, item.name);
    if (item.isDirectory()) {
      // Nếu là thư mục, gọi đệ quy để load commands từ thư mục con
      loadCommandsFromDirectory(itemPath, client, commandsToRegister);
    } else if (item.isFile() && item.name.endsWith('.js')) {
      // Nếu là file .js, load command như bình thường
      try {
        const command = require(itemPath);
        
        // Đảm bảo command có các thuộc tính bắt buộc
        if (!command.data || !command.execute) {
          logger.warn(`Command tại ${itemPath} thiếu thuộc tính bắt buộc 'data' hoặc 'execute'`);
          continue;
        }
        
        // Thêm command vào collection
        client.commands.set(command.data.name, command);
        commandsToRegister.push(command.data.toJSON());
        
        logger.debug(`Đã load command: ${command.data.name} từ ${itemPath}`);
      } catch (error) {
        logger.error(`Lỗi khi load command file ${item.name} từ ${directory}:`, { 
          message: error.message,
          stack: error.stack
        });
      }
    }
  }
}

/**
 * Khởi tạo tất cả các tính năng và đăng ký các handlers
 * @param {Client} client - Discord client instance
 */
function loadFeatures(client) {
  try {
    // Đảm bảo các collections handlers đã được khởi tạo
    if (!client.buttonHandlers) client.buttonHandlers = new Collection();
    if (!client.selectMenuHandlers) client.selectMenuHandlers = new Collection();
    if (!client.modalHandlers) client.modalHandlers = new Collection();
    
    // Load các tính năng từ thư mục features
    const featuresPath = path.join(__dirname, '..', 'features');
    const features = fs.readdirSync(featuresPath, { withFileTypes: true })
      .filter(dirent => dirent.isDirectory())
      .map(dirent => dirent.name);
    
    for (const feature of features) {
      try {
        const featurePath = path.join(featuresPath, feature);
        const featureIndexPath = path.join(featurePath, 'index.js');
        
        if (fs.existsSync(featureIndexPath)) {
          const featureModule = require(featureIndexPath);
          
          // Nếu tính năng có hàm khởi tạo, gọi nó với client
          if (featureModule.init && typeof featureModule.init === 'function') {
            featureModule.init(client);
            logger.info(`Đã khởi tạo tính năng: ${feature}`);
          }
        }
      } catch (error) {
        logger.error(`Lỗi khi khởi tạo tính năng ${feature}:`, { 
          message: error.message,
          stack: error.stack
        });
      }
    }
    
    logger.info('Đã khởi tạo tất cả các tính năng thành công');
  } catch (error) {
    logger.error('Lỗi khi load các tính năng:', { 
      message: error.message,
      stack: error.stack
    });
    throw error;
  }
}

/**
 * Load tất cả các event handlers
 * @param {Client} client - Discord client instance
 */
function loadEvents(client) {
  try {
    // Load events từ thư mục chính
    const eventsPath = path.join(__dirname, '..', 'events');
    if (fs.existsSync(eventsPath)) {
      loadEventsFromDirectory(eventsPath, client);
    }
    
    // Load events từ các thư mục features
    const featuresPath = path.join(__dirname, '..', 'features');
    if (fs.existsSync(featuresPath)) {
      const features = fs.readdirSync(featuresPath, { withFileTypes: true })
        .filter(dirent => dirent.isDirectory())
        .map(dirent => dirent.name);
      
      for (const feature of features) {
        const featureEventsPath = path.join(featuresPath, feature, 'events');
        if (fs.existsSync(featureEventsPath)) {
          loadEventsFromDirectory(featureEventsPath, client);
        }
      }
    }
  } catch (error) {
    logger.error('Lỗi khi load events:', { 
      message: error.message,
      stack: error.stack
    });
    throw error;
  }
}

/**
 * Load các event handlers từ một thư mục cụ thể
 * @param {string} directory - Đường dẫn thư mục
 * @param {Client} client - Discord client instance
 */
function loadEventsFromDirectory(directory, client) {
  const eventFiles = fs.readdirSync(directory, { withFileTypes: true })
    .filter(file => file.isFile() && file.name.endsWith('.js'))
    .map(file => file.name);
    
  for (const file of eventFiles) {
    try {
      const filePath = path.join(directory, file);
      const eventModule = require(filePath);
      
      // Kiểm tra xem module export là một hàm hay một đối tượng event
      if (typeof eventModule === 'function') {
        // Đây có thể là một handler module, bỏ qua nó
        logger.debug(`Bỏ qua file ${filePath} vì nó export một function (handler), không phải event object.`);
        continue;
      }
      
      // Kiểm tra xem module có thuộc tính name và execute không
      if (!eventModule.name || typeof eventModule.execute !== 'function') {
        logger.warn(`Event tại ${filePath} thiếu thuộc tính bắt buộc 'name' hoặc 'execute'`);
        continue;
      }
      
      if (eventModule.once) {
        client.once(eventModule.name, (...args) => eventModule.execute(...args));
      } else {
        client.on(eventModule.name, (...args) => eventModule.execute(...args));
      }
      
      logger.debug(`Đã đăng ký event: ${eventModule.name} từ ${filePath}`);
    } catch (error) {
      logger.error(`Lỗi khi load event file ${file}:`, { 
        message: error.message,
        stack: error.stack
      });
    }
  }
}

module.exports = {
  registerCommands,
  loadCommands,
  loadEvents,
  loadFeatures
}; 