/**
 * @file client.js
 * @description Module cấu hình và khởi tạo Discord client
 * <AUTHOR> Assistant
 * @date 2025-05-19
 */

const { Client, GatewayIntentBits, Partials, Collection } = require('discord.js');
const config = require('./config');
const logger = require('../shared/utils/logger');

/**
 * Khởi tạo Discord client với các intent cần thiết
 * @returns {Client} Discord.js client instance
 */
function createClient() {
  // Cấu hình các intents cần thiết cho bot
  const client = new Client({
    intents: [
      GatewayIntentBits.Guilds,
      GatewayIntentBits.GuildMessages,
      GatewayIntentBits.GuildMembers,
      GatewayIntentBits.MessageContent,
      GatewayIntentBits.GuildMessageReactions
    ],
    partials: [
      Partials.Message,
      Partials.Channel,
      Partials.Reaction
    ]
  });

  // <PERSON><PERSON><PERSON> trữ các commands và events trong Collection
  client.commands = new Collection();
  
  // <PERSON><PERSON> lý lỗi từ client
  client.on('error', error => {
    logger.error('Discord client error:', { 
      message: error.message, 
      stack: error.stack 
    });
  });
  
  // Xử lý cảnh báo từ client
  client.on('warn', warning => {
    logger.warn('Discord client warning:', { warning });
  });
  
  // Xử lý debug thông tin (chỉ trong development)
  if (config.app.nodeEnv === 'development') {
    client.on('debug', info => {
      // Note: Để tránh log quá nhiều, có thể lọc thông tin debug
      if (!info.includes('Heartbeat') && !info.includes('Sending a heartbeat')) {
        logger.debug('Discord client debug:', { info });
      }
    });
  }
  
  return client;
}

module.exports = { createClient }; 