/**
 * @file index.js
 * @description Module quản lý cấu hình và biến môi trường cho bot
 * <AUTHOR> Assistant
 * @date 2025-05-19
 */

// Tải biến môi trường từ file .env
require('dotenv').config();

/**
 * C<PERSON><PERSON> hình chung của <PERSON>ng dụng, tất cả được lấy từ biến môi trường
 * @type {Object}
 */
const config = {
  // Discord Bot Configuration
  bot: {
    token: process.env.DISCORD_TOKEN,
    clientId: process.env.CLIENT_ID,
    guildId: process.env.GUILD_ID
  },
  
  // Database Configuration cho từng tính năng
  database: {
    // Kết nối dành cho tính năng Verify
    verify: {
      host: process.env.VERIFY_DB_HOST,
      port: process.env.VERIFY_DB_PORT || 3306,
      user: process.env.VERIFY_DB_USER,
      password: process.env.VERIFY_DB_PASSWORD,
      database: process.env.VERIFY_DB_NAME,
      waitForConnections: true,
      connectionLimit: 10,
      queueLimit: 0
    }
    // Các kết nối database khác sẽ được thêm vào đây khi cần
  },
  
  // Logging Configuration
  logging: {
    level: process.env.LOG_LEVEL || 'info'
  },
  
  // App Configuration
  app: {
    nodeEnv: process.env.NODE_ENV || 'development',
    websiteUrl: process.env.WEBSITE_URL,
    supportEmail: process.env.SUPPORT_EMAIL
  },

  // Website Configuration
  website: {
    url: process.env.WEBSITE_URL || 'https://example.com',
    supportEmail: process.env.SUPPORT_EMAIL || '<EMAIL>'
  },
  
  // Permissions Configuration
  permissions: {
    // ID của người dùng có quyền admin đặc biệt cho các chức năng nhất định
    // KHÔNG hardcode ID trong code, luôn sử dụng biến môi trường
    verifyAdminId: process.env.VERIFY_ADMIN_ID
  },
  
  // Channels Configuration
  channels: {
    // ID của channel nhận thông báo khi học sinh xác thực thành công 
    adminNotificationChannelId: process.env.ADMIN_NOTIFICATION_CHANNEL_ID || null
  },
  
  // Kiểm tra xem tất cả các biến môi trường bắt buộc đã được cung cấp chưa
  checkRequiredEnvs: () => {
    const requiredEnvs = [
      'DISCORD_TOKEN',
      'CLIENT_ID',
      'GUILD_ID',
      'VERIFY_DB_HOST',
      'VERIFY_DB_USER',
      'VERIFY_DB_PASSWORD',
      'VERIFY_DB_NAME',
      'VERIFY_ADMIN_ID'
    ];
    
    const missingEnvs = requiredEnvs.filter(env => !process.env[env]);
    
    if (missingEnvs.length > 0) {
      throw new Error(
        `Thiếu các biến môi trường sau: ${missingEnvs.join(', ')}. ` +
        'Vui lòng cập nhật file .env của bạn.'
      );
    }
    
    return true;
  }
};

module.exports = config; 