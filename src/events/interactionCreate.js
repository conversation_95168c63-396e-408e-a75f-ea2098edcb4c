/**
 * @file interactionCreate.js
 * @description Event handler đ<PERSON> xử lý các tương tác (slash commands, buttons, select menus) từ người dùng
 * <AUTHOR> Assistant
 * @date 2025-05-19
 */

const logger = require('../shared/utils/logger');

module.exports = {
  name: 'interactionCreate',
  async execute(interaction) {
    try {
      // Log thông tin về interaction nhận được
      const user = interaction.user;
      const guild = interaction.guild;
      const channel = interaction.channel;
      
      const contextInfo = {
        userId: user.id,
        username: user.username,
        guildId: guild?.id,
        guildName: guild?.name,
        channelId: channel?.id,
        channelName: channel?.name,
      };
      
      // X<PERSON> lý slash commands
      if (interaction.isChatInputCommand()) {
        const command = interaction.client.commands.get(interaction.commandName);
        
        if (!command) {
          logger.warn(`Không tìm thấy command: ${interaction.commandName}`, contextInfo);
          await interaction.reply({ 
            content: '<PERSON><PERSON><PERSON> này không tồn tại hoặc chưa được cài đặt đúng cách', 
            ephemeral: true 
          });
          return;
        }
        
        logger.info(`Thực thi command: ${interaction.commandName}`, contextInfo);
        
        // Thực thi command
        await command.execute(interaction);
        return;
      }
      
      // Xử lý button interactions
      if (interaction.isButton()) {
        logger.info(`Button interaction: ${interaction.customId}`, contextInfo);
        
        // Phân tích customId để xác định loại button
        // Format: feature:action:params
        const [feature, action, ...params] = interaction.customId.split(':');
        
        // Tìm handler tương ứng trong client.buttonHandlers (nếu được cài đặt)
        const buttonHandlers = interaction.client.buttonHandlers;
        
        if (buttonHandlers && buttonHandlers.has(feature)) {
          await buttonHandlers.get(feature)(interaction, action, params);
        } else {
          logger.warn(`Không tìm thấy handler cho button: ${interaction.customId}`, contextInfo);
          await interaction.reply({ 
            content: 'Không thể xử lý tương tác này. Vui lòng thử lại sau.', 
            ephemeral: true 
          });
        }
        
        return;
      }
      
      // Xử lý select menu interactions
      if (interaction.isStringSelectMenu() || interaction.isUserSelectMenu() || interaction.isRoleSelectMenu() || interaction.isChannelSelectMenu() || interaction.isMentionableSelectMenu()) {
        logger.info(`Select menu interaction: ${interaction.customId}`, contextInfo);
        
        // Tương tự như button, phân tích customId
        const [feature, action, ...params] = interaction.customId.split(':');
        
        // Tìm handler tương ứng trong client.selectMenuHandlers (nếu được cài đặt)
        const selectMenuHandlers = interaction.client.selectMenuHandlers;
        
        if (selectMenuHandlers && selectMenuHandlers.has(feature)) {
          await selectMenuHandlers.get(feature)(interaction, action, params);
        } else {
          logger.warn(`Không tìm thấy handler cho select menu: ${interaction.customId}`, contextInfo);
          await interaction.reply({ 
            content: 'Không thể xử lý tương tác này. Vui lòng thử lại sau.', 
            ephemeral: true 
          });
        }
        
        return;
      }
      
      // Xử lý modal submissions
      if (interaction.isModalSubmit()) {
        logger.info(`Modal submission: ${interaction.customId}`, contextInfo);
        
        // Tương tự, phân tích customId
        const [feature, action, ...params] = interaction.customId.split(':');
        
        // Tìm handler tương ứng trong client.modalHandlers (nếu được cài đặt)
        const modalHandlers = interaction.client.modalHandlers;
        
        if (modalHandlers && modalHandlers.has(feature)) {
          await modalHandlers.get(feature)(interaction, action, params);
        } else {
          logger.warn(`Không tìm thấy handler cho modal: ${interaction.customId}`, contextInfo);
          await interaction.reply({ 
            content: 'Không thể xử lý form này. Vui lòng thử lại sau.', 
            ephemeral: true 
          });
        }
        
        return;
      }
      
      // Các loại tương tác khác chưa được xử lý
      logger.debug(`Tương tác không được xử lý: ${interaction.type}`, contextInfo);
      
    } catch (error) {
      logger.error('Lỗi khi xử lý interaction:', { 
        interactionType: interaction.type,
        interactionId: interaction.id,
        commandName: interaction.isChatInputCommand() ? interaction.commandName : null,
        customId: interaction.isButton() || interaction.isSelectMenu() || interaction.isModalSubmit() ? interaction.customId : null,
        message: error.message,
        stack: error.stack
      });
      
      // Trả về lỗi cho người dùng nếu tương tác vẫn còn hiệu lực
      if (interaction.deferred || interaction.replied) {
        await interaction.followUp({ 
          content: 'Có lỗi xảy ra khi xử lý yêu cầu của bạn. Vui lòng thử lại sau.', 
          ephemeral: true 
        }).catch(() => {});
      } else {
        await interaction.reply({ 
          content: 'Có lỗi xảy ra khi xử lý yêu cầu của bạn. Vui lòng thử lại sau.', 
          ephemeral: true 
        }).catch(() => {});
      }
    }
  }
}; 