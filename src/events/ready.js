/**
 * @file ready.js
 * @description Event handler cho sự kiện ready khi bot khởi động
 * <AUTHOR> Assistant
 * @date 2025-05-19
 */

const logger = require('../shared/utils/logger');

module.exports = {
  name: 'ready',
  once: true,
  execute(client) {
    const guildCount = client.guilds.cache.size;
    const userCount = client.users.cache.size;
    
    logger.info(`Bot đã sẵn sàng! Đăng nhập với tên ${client.user.tag}`);
    logger.info(`Đang phục vụ ${guildCount} servers và ${userCount} users`);
    
    // Thiết lập presence cho bot
    client.user.setPresence({
      activities: [{ 
        name: 'Ông Ba Dạy Hoá', 
        type: 'WATCHING' 
      }],
      status: 'online'
    });
    
    // Log danh sách servers nếu ở chế độ development
    if (process.env.NODE_ENV === 'development') {
      const guildList = client.guilds.cache.map(guild => `${guild.name} (${guild.id})`).join('\n- ');
      logger.debug(`Danh sách servers:\n- ${guildList}`);
    }
  }
}; 