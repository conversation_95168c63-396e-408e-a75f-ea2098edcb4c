# Tính năng Xác thực (Verify)

## <PERSON><PERSON> tả tổng quan
Tính năng này cho phép học sinh đã mua khóa học trên website Ông Ba Dạy Hoá xác thực trong Discord server để nhận các quyền truy cập vào các kênh học tập riêng tương ứng với khóa học đã mua. Quá trình xác thực dựa trên email, mã kích hoạt mà học sinh nhận được sau khi hoàn tất giao dịch mua khóa học, và số điện thoại phụ huynh để hỗ trợ liên lạc khi cần thiết.

## Các lệnh chính và cách sử dụng
- **/verify**: Lệnh dành cho quản trị viên để tạo một button xác thực trong một kênh cụ thể. <PERSON><PERSON><PERSON> sinh sẽ nhấn button này để bắt đầu quá trình xác thực.
- **(Button) <PERSON><PERSON><PERSON> thực**: <PERSON>hi học sinh nhấn button, một modal sẽ hiện ra yêu cầu nhập email, mã kích hoạt đã nhận được khi mua khóa học, và số điện thoại phụ huynh.
- **(Button) Hỗ trợ qua Zalo**: Nút liên kết cho phép học sinh liên hệ trực tiếp với quản trị viên qua Zalo khi cần hỗ trợ trong quá trình xác thực.

## Luồng hoạt động chính
```mermaid
graph TD
    A[Học sinh mua khóa học trên website] --> B[Nhận mã kích hoạt qua email]
    B --> C[Vào kênh Discord và nhấn button Xác thực]
    C --> D[Điền form với email, mã kích hoạt và số điện thoại phụ huynh]
    D --> E{Kiểm tra thông tin}
    E -- Thông tin hợp lệ --> F[Kiểm tra học sinh đã mua khóa học nào]
    F --> G[Cấp role tương ứng với khóa học]
    G --> H[Thông báo xác thực thành công]
    G --> I[Gửi thông báo đến kênh admin]
    E -- Thông tin không hợp lệ --> J[Thông báo lỗi chi tiết]
    J --> K[Hiển thị nút Nhập lại và Hỗ trợ]
    K --> C
```

## Các Use Cases chi tiết
```mermaid
sequenceDiagram
    participant S as Học sinh
    participant B as Bot Discord
    participant DB as Database
    participant A as Kênh Admin

    S->>B: Nhấn button Xác thực
    B->>S: Hiển thị form nhập email, code và số điện thoại phụ huynh
    S->>B: Gửi thông tin (email, code, số điện thoại phụ huynh)
    B->>B: Kiểm tra định dạng email, code và số điện thoại phụ huynh
    alt Định dạng không hợp lệ
        B->>S: Thông báo lỗi định dạng + nút Nhập lại + nút Hỗ trợ Zalo
        S->>B: Nhấn nút Nhập lại
    else Định dạng hợp lệ
        B->>DB: Kiểm tra thông tin

        alt Thông tin hợp lệ & Chưa kích hoạt
            DB->>B: Trả về thông tin khóa học và role ID
            B->>S: Cấp role tương ứng
            B->>DB: Cập nhật trạng thái đã kích hoạt
            B->>S: Thông báo xác thực thành công + nút Hỗ trợ Zalo
            B->>A: Gửi thông báo chi tiết về học sinh xác thực thành công
        else Đã kích hoạt
            DB->>B: Thông báo đã kích hoạt
            B->>S: Thông báo đã kích hoạt trước đó + nút Hỗ trợ Zalo
        else Email đã được liên kết với tài khoản Discord khác
            DB->>B: Trả về thông tin tài khoản đã liên kết
            B->>S: Thông báo email đã được sử dụng bởi tài khoản khác + nút Nhập lại + nút Hỗ trợ Zalo
        else Email/code không khớp
            DB->>B: Không tìm thấy thông tin
            B->>S: Thông báo email hoặc mã không chính xác + nút Nhập lại + nút Hỗ trợ Zalo
        else Không tìm thấy khóa học
            DB->>B: Trả về danh sách khóa học trống
            B->>S: Thông báo không tìm thấy khóa học + nút Nhập lại + nút Hỗ trợ Zalo
        else Lỗi hệ thống
            DB->>B: Ném lỗi
            B->>S: Thông báo lỗi hệ thống + nút Nhập lại + nút Hỗ trợ Zalo
        end
    end
```

## Cơ chế xử lý lỗi
Tính năng Verify được thiết kế với cơ chế xử lý lỗi toàn diện nhằm đảm bảo trải nghiệm người dùng tốt và giúp học sinh dễ dàng giải quyết vấn đề:

1. **Kiểm tra định dạng đầu vào (ngay từ client-side)**:
   - Kiểm tra email theo regex `^[^\s@]+@[^\s@]+\.[^\s@]+$`: Thông báo "Email không đúng định dạng. Vui lòng kiểm tra và nhập lại"
   - Kiểm tra mã kích hoạt theo regex `^[A-Z0-9]{7}$`: Thông báo "Mã kích hoạt phải có 7 ký tự chữ cái A-Z và số 0-9 in hoa"
   - Kiểm tra số điện thoại phụ huynh theo regex `^[0-9]{10}$`: Thông báo "Số điện thoại phụ huynh phải là 10 chữ số"

2. **Lỗi đầu vào (server-side)**:
   - Kiểm tra email rỗng hoặc thiếu: Thông báo "Vui lòng cung cấp đầy đủ email, mã kích hoạt và số điện thoại phụ huynh"
   - Kiểm tra mã kích hoạt rỗng: Thông báo "Vui lòng cung cấp đầy đủ email, mã kích hoạt và số điện thoại phụ huynh"
   - Kiểm tra số điện thoại phụ huynh rỗng: Thông báo "Vui lòng cung cấp đầy đủ email, mã kích hoạt và số điện thoại phụ huynh"
   - Kiểm tra định dạng email không hợp lệ: Thông báo "Định dạng email không hợp lệ. Vui lòng kiểm tra lại"
   - Kiểm tra định dạng số điện thoại phụ huynh không hợp lệ: Thông báo "Số điện thoại phụ huynh phải là 10 chữ số"

3. **Lỗi xác thực**:
   - Email/mã không tồn tại: Thông báo "Email hoặc mã kích hoạt không chính xác"
   - Mã đã được kích hoạt: Thông báo "Mã kích hoạt này đã được sử dụng trước đó"
   - Email đã liên kết với tài khoản Discord khác: Thông báo "Email này đã được liên kết với một tài khoản Discord khác"
   - Không tìm thấy khóa học: Thông báo "Không tìm thấy khóa học nào được liên kết với email này"

4. **Lỗi hệ thống**:
   - Lỗi khi cập nhật database: Thông báo "Có lỗi xảy ra khi xác thực. Vui lòng thử lại sau"
   - Lỗi khi cấp role Discord: Vẫn xác thực thành công nhưng thông báo chi tiết về khóa học nào chưa được cấp quyền
   - Lỗi bất ngờ khác: Thông báo "Đã xảy ra lỗi trong quá trình xác thực. Vui lòng thử lại sau hoặc liên hệ admin"

5. **Cải thiện UX cho xử lý lỗi**:
   - Các thông báo lỗi đều đi kèm nút "Nhập lại" giúp học sinh dễ dàng thử lại ngay
   - Tất cả các thông báo (lỗi và thành công) đều có nút "Hỗ trợ qua Zalo" giúp học sinh dễ dàng liên hệ admin khi cần

Tất cả các lỗi đều được ghi lại (log) chi tiết với đầy đủ thông tin context để phục vụ việc debug và cải thiện hệ thống.

## Thông báo cho Admin

Khi một học sinh xác thực thành công, hệ thống sẽ tự động gửi thông báo đến kênh admin được cấu hình trong biến môi trường `ADMIN_NOTIFICATION_CHANNEL_ID`. Thông báo này bao gồm:

- Tên học sinh
- Discord tag và ID
- Email
- Số điện thoại phụ huynh
- Thời gian xác thực
- Danh sách khóa học đã cấp quyền

Tính năng này giúp quản trị viên dễ dàng theo dõi và quản lý các học sinh mới tham gia server.

## Cấu trúc dữ liệu

### Bảng dữ liệu chính:
1. **activation_codes**: Lưu trữ mã kích hoạt
   - `id`: ID của mã kích hoạt
   - `code`: Mã kích hoạt
   - `activated_at`: Thời điểm mã được kích hoạt (NULL nếu chưa kích hoạt)
   - `discord_status`: Trạng thái Discord ('0': chưa kích hoạt, '1': đã kích hoạt)

2. **up_users**: Người dùng (học sinh)
   - `id`: ID của người dùng
   - `email`: Email của học sinh
   - `fullname`: Tên đầy đủ của học sinh
   - `user_discord_id`: ID Discord của học sinh (NULL nếu chưa liên kết)
   - `parents_phone`: Số điện thoại phụ huynh

3. **orders**: Đơn hàng
   - `id`: ID của đơn hàng
   - `payment_status`: Trạng thái thanh toán ('completed', 'pending', etc.)

4. **courses**: Khóa học
   - `id`: ID của khóa học
   - `title`: Tên khóa học

5. **course_tiers**: Cấp độ khóa học
   - `id`: ID của cấp độ
   - `tier_name`: Tên cấp độ (Standard, Premium, etc.)
   - `discord_role_id`: ID của role Discord tương ứng

### Các bảng liên kết:
- **orders_activation_codes_lnk**: Liên kết đơn hàng với mã kích hoạt
- **orders_users_permissions_user_lnk**: Liên kết đơn hàng với người dùng
- **orders_course_lnk**: Liên kết đơn hàng với khóa học
- **orders_course_tier_lnk**: Liên kết đơn hàng với cấp độ khóa học

## Trường hợp đặc biệt

1. **Học sinh mua nhiều khóa học**:
   - Hệ thống sẽ cấp tất cả các role Discord tương ứng với các khóa học đã mua
   - Danh sách khóa học sẽ được hiển thị đầy đủ trong thông báo xác thực thành công

2. **Role Discord không tồn tại**:
   - Nếu `discord_role_id` của một khóa học không tồn tại trong server, hệ thống vẫn hoàn tất xác thực
   - Thông báo chi tiết về khóa học nào chưa được cấp quyền
   - Yêu cầu học sinh liên hệ admin để được hỗ trợ

3. **Học sinh đã có role**:
   - Hệ thống kiểm tra nếu học sinh đã có role của một khóa học, sẽ không cấp lại
   - Vẫn hiển thị đầy đủ tất cả các khóa học trong thông báo thành công

4. **Xử lý lỗi trong quá trình cấp role**:
   - Nếu việc cấp một role nào đó gặp lỗi, hệ thống vẫn tiếp tục xử lý các role khác
   - Đảm bảo không bị gián đoạn hoàn toàn nếu chỉ một phần gặp lỗi

## Phát triển trong tương lai

1. **Quản lý và gửi lại mã kích hoạt** cho học sinh nếu bị mất
2. **Dashboard quản lý** thông tin xác thực và theo dõi học sinh đã kích hoạt
3. **API xác thực** để tích hợp với các hệ thống khác
4. **Hệ thống quản lý role theo thời gian** để tự động thu hồi quyền khi khóa học hết hạn
5. **Thống kê tự động** về số lượng học sinh đã xác thực, phân bố theo khóa học

## Cấu trúc thư mục

```
src/features/verify/
├── README.md                 # Tài liệu này
├── index.js                  # Entry point của tính năng
├── commands/                 # Các lệnh Discord
│   └── verify_command.js     # Lệnh /verify
├── events/                   # Các event handler
│   ├── verify_button_event.js # Xử lý sự kiện nhấn button
│   └── verify_modal_event.js  # Xử lý sự kiện submit modal
├── models/                   # Các model tương tác với database
│   └── verify_model.js       # Model xử lý dữ liệu xác thực
├── services/                 # Các service xử lý logic
│   └── verify_service.js     # Service xử lý logic xác thực
└── utils/                    # Các tiện ích
    └── verify_utils.js       # Các hàm tiện ích
```

## Cài đặt và cấu hình

### Yêu cầu
- Node.js v16 trở lên
- MySQL/MariaDB
- Discord Bot Token với các quyền:
  - `MANAGE_ROLES`
  - `MANAGE_NICKNAMES`
  - `SEND_MESSAGES`
  - `VIEW_CHANNELS`
  - `EMBED_LINKS`

### Cấu hình
Tính năng verify sử dụng các biến môi trường sau:
- `ADMIN_NOTIFICATION_CHANNEL_ID`: ID của kênh Discord để gửi thông báo khi có học sinh xác thực thành công
- `VERIFY_ADMIN_ID`: ID của người dùng Discord có quyền sử dụng lệnh /verify

## Cách kiểm thử

Tất cả các thành phần của tính năng Verify đều có unit test đầy đủ:
- Test các trường hợp đầu vào khác nhau
- Test kết quả trả về từ database
- Test việc cấp role Discord
- Test các kịch bản lỗi
- Test việc gửi thông báo đến kênh admin

### Chạy test
```bash
# Chạy tất cả các test cho tính năng verify
npm test -- --testPathPattern=verify

# Chạy test cho một thành phần cụ thể
npm test -- --testPathPattern=verify_service
npm test -- --testPathPattern=verify_model
npm test -- --testPathPattern=verify_utils
npm test -- --testPathPattern=verify_command
npm test -- --testPathPattern=verify_button_event
npm test -- --testPathPattern=verify_modal_event
npm test -- --testPathPattern=verify/index
```

### Độ bao phủ code
Hiện tại, độ bao phủ code của tính năng verify đạt:
- `verify_service.js`: 95.65% (line coverage), 82.55% (branch coverage)
- `verify_model.js`: 100% (line coverage), 100% (branch coverage)
- `verify_utils.js`: 100% (line coverage), 100% (branch coverage)
- `verify_command.js`: 100% (line coverage), 100% (branch coverage)
- `verify_button_event.js`: 100% (line coverage), 100% (branch coverage)
- `verify_modal_event.js`: 100% (line coverage), 90% (branch coverage)
- `index.js`: 100% (line coverage), 100% (branch coverage)

## Troubleshooting

### Vấn đề thường gặp

1. **Không thể cấp role cho học sinh**
   - Kiểm tra quyền của bot: Bot cần có quyền `MANAGE_ROLES`
   - Kiểm tra thứ tự role: Role của bot phải cao hơn role cần cấp
   - Kiểm tra `discord_role_id` trong database: ID role phải chính xác và tồn tại trong server

2. **Không thể đổi nickname của học sinh**
   - Kiểm tra quyền của bot: Bot cần có quyền `MANAGE_NICKNAMES`
   - Kiểm tra thứ tự role: Bot không thể đổi nickname của người dùng có role cao hơn
   - Kiểm tra giới hạn của Discord: Nickname không được quá 32 ký tự

3. **Không nhận được thông báo trong kênh admin**
   - Kiểm tra `ADMIN_NOTIFICATION_CHANNEL_ID` trong cấu hình
   - Kiểm tra quyền của bot: Bot cần có quyền `SEND_MESSAGES` và `EMBED_LINKS` trong kênh admin

4. **Lỗi khi truy vấn database**
   - Kiểm tra kết nối database
   - Kiểm tra cấu trúc bảng và các trường
   - Kiểm tra log lỗi chi tiết trong console

## Changelog

### v1.1.0 (2025-05-14)
- Thêm trường số điện thoại phụ huynh vào form xác thực
- Cập nhật modal xác thực để bao gồm trường nhập số điện thoại phụ huynh
- Thêm validation cho số điện thoại phụ huynh (10 chữ số)
- Cập nhật database để lưu trữ số điện thoại phụ huynh
- Cập nhật unit test để kiểm tra đầy đủ các thành phần mới
- Cải thiện xử lý lỗi cho trường số điện thoại phụ huynh
- Cập nhật tài liệu hướng dẫn

### v1.0.0 (2023-05-09)
- Phát hành phiên bản đầu tiên của tính năng verify
- Hỗ trợ xác thực học sinh thông qua email và mã kích hoạt
- Tự động cấp role Discord dựa trên khóa học đã mua
- Thông báo chi tiết cho học sinh và admin
- Cải thiện xử lý lỗi khi cấp role Discord
- Thêm thông báo chi tiết về các khóa học chưa được cấp quyền
- Tối ưu hóa truy vấn database
- Thêm hỗ trợ cho học sinh mua nhiều khóa học
- Cải thiện giao diện thông báo xác thực
- Thêm nút "Nhập lại" để dễ dàng thử lại khi gặp lỗi
- Thêm tính năng tự động đổi nickname của học sinh theo tên thật
- Cải thiện bảo mật, chống SQL injection và XSS
- Tối ưu hóa hiệu suất khi xử lý nhiều yêu cầu xác thực cùng lúc
- Thêm unit test đầy đủ cho tất cả các thành phần
- Đạt độ bao phủ code trên 95%
- Cải thiện tài liệu hướng dẫn