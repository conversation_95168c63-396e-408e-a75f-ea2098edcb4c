/**
 * @file verify_command.js
 * @description Slash command để tạo button xác thực học sinh
 * <AUTHOR> Assistant
 * @date 2025-05-09
 */

const { SlashCommandBuilder, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle, PermissionFlagsBits } = require('discord.js');
const logger = require('../../../shared/utils/logger');
const config = require('../../../core/config');
const verifyUtils = require('../utils/verify_utils');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('verify')
    .setDescription('Tạo button xác thực dành cho học sinh đã mua khóa học')
    .setDefaultMemberPermissions(PermissionFlagsBits.Administrator),

  /**
   * Thực thi lệnh verify
   * @param {import('discord.js').ChatInputCommandInteraction} interaction
   */
  async execute(interaction) {
    try {
      // Kiểm tra nếu người dùng không phải là admin được chỉ định
      if (interaction.user.id !== config.permissions.verifyAdminId) {
        await interaction.reply({
          content: '❌ Bạn không có quyền sử dụng lệnh này. Chỉ admin được chỉ định mới có thể tạo button xác thực.',
          ephemeral: true
        });
        logger.warn(`${interaction.user.tag} đã cố gắng sử dụng lệnh verify nhưng không được phép`);
        return;
      }

      // Tạo embed thông báo
      const embed = new EmbedBuilder()
        .setTitle('🔐 Xác thực Học sinh Ông Ba Dạy Hoá')
        .setDescription('Nếu bạn đã mua khóa học trên website của Ông Ba Dạy Hoá, hãy nhấn vào nút bên dưới để xác thực và nhận role Discord tương ứng.\n\n**Bạn sẽ cần cung cấp:**\n- Email đăng ký trên website\n- Mã kích hoạt (đã được gửi qua email)')
        .setColor(0x3498db)
        .setFooter({
          text: 'Ông Ba Dạy Hoá - Học hoá dễ như ăn kẹo',
        })
        .setTimestamp();

      // Tạo button sử dụng utils
      const row = verifyUtils.createVerifyButton();

      // Gửi embed và button
      await interaction.reply({
        content: 'Button xác thực đã được tạo bên dưới:',
        ephemeral: true
      });

      await interaction.channel.send({
        embeds: [embed],
        components: [row]
      });

      logger.info(`${interaction.user.tag} đã tạo button xác thực trong kênh #${interaction.channel.name}`);
    } catch (error) {
      logger.error('Lỗi khi tạo button xác thực:', {
        message: error.message,
        stack: error.stack,
        user: interaction.user.tag,
        channel: interaction.channel?.name
      });

      await interaction.reply({
        content: '❌ Đã xảy ra lỗi khi tạo button xác thực. Vui lòng thử lại sau.',
        ephemeral: true
      });
    }
  }
};