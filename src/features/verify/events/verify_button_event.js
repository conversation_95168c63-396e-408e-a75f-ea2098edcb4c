/**
 * @file verify_button_event.js
 * @description Xử lý sự kiện khi người dùng nhấn vào button xác thực
 * <AUTHOR> Assistant
 * @date 2025-05-09
 */

const verifyUtils = require('../utils/verify_utils');
const logger = require('../../../shared/utils/logger');
const { ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');

/**
 * Handler cho các button liên quan đến tính năng verify
 * @param {import('discord.js').ButtonInteraction} interaction - Discord button interaction
 * @param {string} action - Hành động của button (ví dụ: "button")
 * @param {string[]} params - <PERSON><PERSON><PERSON> tham số bổ sung nếu có (ví dụ: "verify")
 */
async function handleVerifyButton(interaction, action, params) {
  try {
    // Kiểm tra nếu đây là button xác thực
    if (action !== 'button' || !params.includes('verify')) {
      return;
    }

    logger.info(`Người dùng ${interaction.user.tag} đã nhấn button xác thực`);

    // Tạo và hiển thị modal xác thực
    const modal = verifyUtils.createVerifyModal();

    // Hiển thị modal cho người dùng
    await interaction.showModal(modal);
  } catch (error) {
    logger.error('Lỗi khi xử lý button xác thực:', {
      message: error.message,
      stack: error.stack,
      user: interaction.user?.tag
    });

    // Thông báo lỗi cho người dùng nếu có thể
    try {
      if (!interaction.replied && !interaction.deferred) {
        /**
         * Tạo nút hỗ trợ Zalo cho thông báo lỗi
         * Đảm bảo người dùng dễ dàng liên hệ hỗ trợ khi gặp vấn đề
         */
        const supportButton = new ButtonBuilder()
          .setLabel('Hỗ trợ qua Zalo')
          .setStyle(ButtonStyle.Link)
          .setURL('https://zalo.me/0828949479')
          .setEmoji('💬');

        const actionRow = new ActionRowBuilder().addComponents(supportButton);

        await interaction.reply({
          content: '❌ Đã xảy ra lỗi khi mở form xác thực. Vui lòng thử lại sau hoặc liên hệ hỗ trợ.',
          components: [actionRow],
          ephemeral: true
        });
      }
    } catch (replyError) {
      // Bỏ qua lỗi khi không thể trả lời
      logger.error('Không thể gửi thông báo lỗi:', replyError.message);
    }
  }
}

// Đăng ký handler cho tính năng verify
module.exports = (client) => {
  client.buttonHandlers.set('verify', handleVerifyButton);
  logger.info('Đã đăng ký handler cho các button xác thực (verify)');
};