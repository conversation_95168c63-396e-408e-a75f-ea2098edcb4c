/**
 * @file verify_modal_event.js
 * @description Xử lý sự kiện khi người dùng submit modal xác thực
 * <AUTHOR> Assistant
 * @date 2025-05-09
 */

const verifyService = require('../services/verify_service');
const verifyUtils = require('../utils/verify_utils');
const logger = require('../../../shared/utils/logger');
const { ButtonBuilder, ButtonStyle, ActionRowBuilder } = require('discord.js');

/**
 * Tạo các nút hành động cho thông báo lỗi
 * @param {string} customId - Custom ID cho nút nhập lại
 * @returns {ActionRowBuilder} ActionRow chứa các nút
 * @description Tạo một hàng action row với nút "Nhập lại" và nút "Hỗ trợ qua Zalo" để
 *              cải thiện trải nghiệm người dùng khi gặp lỗi, gi<PERSON><PERSON> họ dễ dàng thử lại
 *              hoặc liên hệ hỗ trợ khi cần thiết
 */
function createErrorButtons(customId) {
  // Nút nhập lại
  const retryButton = new ButtonBuilder()
    .setCustomId(customId)
    .setLabel('Nhập lại')
    .setStyle(ButtonStyle.Primary)
    .setEmoji('🔄');

  // Nút hỗ trợ qua Zalo
  const supportButton = new ButtonBuilder()
    .setLabel('Hỗ trợ qua Zalo')
    .setStyle(ButtonStyle.Link)
    .setURL('https://zalo.me/0828949479')
    .setEmoji('💬');

  return new ActionRowBuilder().addComponents(retryButton, supportButton);
}

/**
 * Handler cho các modal liên quan đến tính năng verify
 * @param {import('discord.js').ModalSubmitInteraction} interaction - Discord modal submit interaction
 * @param {string} action - Hành động của modal (ví dụ: "modal")
 * @param {string[]} params - Các tham số bổ sung nếu có (ví dụ: "submit")
 */
async function handleVerifyModal(interaction, action, params) {
  try {
    // Kiểm tra nếu đây là modal xác thực
    if (action !== 'modal' || !params.includes('submit')) {
      return;
    }

    // Thông báo đang xử lý để người dùng đợi
    await interaction.deferReply({ ephemeral: true });

    // Lấy giá trị từ các trường input
    const email = interaction.fields.getTextInputValue('email_input').trim();
    const activationCode = interaction.fields.getTextInputValue('code_input').trim();
    const parentsPhone = interaction.fields.getTextInputValue('parents_phone_input').trim();

    logger.info(`Người dùng ${interaction.user.tag} đã submit form xác thực với email: ${email}, SĐT phụ huynh: ${parentsPhone}`);

    // Kiểm tra định dạng email trước khi gửi đến service
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      logger.warn(`Email không hợp lệ: ${email} từ người dùng ${interaction.user.tag}`);

      await interaction.editReply({
        embeds: [{
          title: '❌ Thông tin không hợp lệ',
          description: 'Email không đúng định dạng. Vui lòng kiểm tra và nhập lại hoặc liên hệ hỗ trợ.',
          color: 0xff0000,
          timestamp: new Date()
        }],
        components: [createErrorButtons('verify:button:verify')],
        ephemeral: true
      });
      return;
    }

    // Kiểm tra định dạng mã kích hoạt (7 ký tự, chữ cái A-Z và số 0-9 in hoa)
    const activationCodeRegex = /^[A-Z0-9]{7}$/;
    if (!activationCodeRegex.test(activationCode)) {
      logger.warn(`Mã kích hoạt không hợp lệ: ${activationCode} từ người dùng ${interaction.user.tag}`);

      await interaction.editReply({
        embeds: [{
          title: '❌ Thông tin không hợp lệ',
          description: 'Mã kích hoạt phải có 7 ký tự chữ cái A-Z và số 0-9 in hoa. Vui lòng kiểm tra và nhập lại hoặc liên hệ hỗ trợ.',
          color: 0xff0000,
          timestamp: new Date()
        }],
        components: [createErrorButtons('verify:button:verify')],
        ephemeral: true
      });
      return;
    }

    // Kiểm tra định dạng SĐT phụ huynh (10 chữ số)
    const phoneRegex = /^\d{10}$/;
    if (!phoneRegex.test(parentsPhone)) {
      logger.warn(`SĐT phụ huynh không hợp lệ: ${parentsPhone} từ người dùng ${interaction.user.tag}`);
      await interaction.editReply({
        embeds: [{
          title: '❌ Thông tin không hợp lệ',
          description: 'Số điện thoại phụ huynh phải là 10 chữ số. Vui lòng kiểm tra và nhập lại hoặc liên hệ hỗ trợ.',
          color: 0xff0000,
          timestamp: new Date()
        }],
        components: [createErrorButtons('verify:button:verify')],
        ephemeral: true
      });
      return;
    }

    // Xác thực người dùng
    const verificationResult = await verifyService.verifyStudent(
      email,
      activationCode,
      interaction.user,
      interaction.member,
      interaction.guild,
      parentsPhone
    );

    // Tạo embed phản hồi dựa vào kết quả xác thực
    let embedResult;
    if (verificationResult.success) {
      // Tạo danh sách các khóa học đã cấp quyền thành công
      const courseList = verificationResult.courses && verificationResult.courses.length > 0
        ? verificationResult.courses.join('\n')
        : 'Không có khóa học nào được tìm thấy';

      // Tạo embed thành công
      embedResult = verifyService.createVerificationResultEmbed(
        true,
        verificationResult.message,
        verificationResult.courses,
        verificationResult.studentName
      );

      // Thêm thông tin về các khóa học không thể cấp quyền (nếu có)
      if (verificationResult.failedCourses && verificationResult.failedCourses.length > 0) {
        embedResult.embed.addFields({
          name: '⚠️ Lưu ý: Các khóa học chưa được cấp quyền',
          value: verificationResult.failedCourses.join('\n')
        });
      }

      // Thêm thông tin về việc đổi nickname (nếu cần)
      if (!verificationResult.nicknameUpdated && verificationResult.studentName) {
        embedResult.embed.addFields({
          name: '📝 Lưu ý về tên hiển thị',
          value: 'Bot không thể tự động đổi tên hiển thị của bạn. Bạn có thể tự đổi nickname trong server để hiển thị tên thật.'
        });
      }

      // Gửi phản hồi thành công cho người dùng
      await interaction.editReply({
        embeds: [embedResult.embed],
        components: [embedResult.actionRow],
        ephemeral: true
      });
    } else {
      // Tạo embed thất bại
      embedResult = verifyService.createVerificationResultEmbed(
        false,
        verificationResult.message || 'Email hoặc mã kích hoạt không chính xác. Vui lòng kiểm tra lại hoặc liên hệ hỗ trợ.'
      );

      // Gửi phản hồi thất bại kèm nút nhập lại và hỗ trợ
      await interaction.editReply({
        embeds: [embedResult.embed],
        components: [createErrorButtons('verify:button:verify')],
        ephemeral: true
      });
    }

    logger.info(`Xác thực ${verificationResult.success ? 'thành công' : 'thất bại'} cho người dùng ${interaction.user.tag}`);
  } catch (error) {
    logger.error('Lỗi khi xử lý modal xác thực:', {
      message: error.message,
      stack: error.stack,
      user: interaction.user?.tag
    });

    // Thông báo lỗi cho người dùng
    try {
      // Tạo nút hỗ trợ Zalo cho phần xử lý lỗi
      if (interaction.deferred) {
        await interaction.editReply({
          embeds: [{
            title: '❌ Đã xảy ra lỗi',
            description: 'Có lỗi xảy ra trong quá trình xử lý. Vui lòng thử lại sau hoặc liên hệ hỗ trợ.',
            color: 0xff0000,
            timestamp: new Date()
          }],
          components: [createErrorButtons('verify:button:verify')],
          ephemeral: true
        });
      } else if (!interaction.replied) {
        await interaction.reply({
          embeds: [{
            title: '❌ Đã xảy ra lỗi',
            description: 'Có lỗi xảy ra trong quá trình xử lý. Vui lòng thử lại sau hoặc liên hệ hỗ trợ.',
            color: 0xff0000,
            timestamp: new Date()
          }],
          components: [createErrorButtons('verify:button:verify')],
          ephemeral: true
        });
      }
    } catch (replyError) {
      logger.error('Không thể gửi thông báo lỗi:', replyError.message);
    }
  }
}

// Đăng ký handler cho tính năng verify
module.exports = (client) => {
  client.modalHandlers.set('verify', handleVerifyModal);
  logger.info('Đã đăng ký handler cho các modal xác thực (verify)');
};