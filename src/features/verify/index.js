/**
 * @file index.js
 * @description File xuất các thành phần chính của tính năng xác thực (verify)
 * <AUTHOR> Assistant
 * @date 2025-05-09
 */

const verifyCommand = require('./commands/verify_command');
const verifyButtonHandler = require('./events/verify_button_event');
const verifyModalHandler = require('./events/verify_modal_event');
const logger = require('../../shared/utils/logger');

/**
 * Khởi tạo tính năng verify, đăng ký các handlers và commands
 * @param {import('discord.js').Client} client - Discord client
 */
function initFeature(client) {
  // Đăng ký button và modal handlers
  verify<PERSON><PERSON>on<PERSON><PERSON><PERSON>(client);
  verifyModalHandler(client);

  logger.info('Tính năng verify đã được khởi tạo thành công');
}

module.exports = {
  // Danh sách các commands
  commands: [verifyCommand],

  // Hàm khởi tạo tính năng
  init: initFeature,

  // Thông tin về tính năng
  info: {
    name: 'Verify',
    description: 'T<PERSON>h năng xác thực học sinh đã mua khóa học',
    version: '1.0.0'
  }
};