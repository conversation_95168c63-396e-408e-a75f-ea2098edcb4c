/**
 * @file verify_model.js
 * @description Model quản lý tương tác với database cho tính năng xác thực (verify)
 * <AUTHOR> Assistant
 * @date 2025-05-09
 */

const db = require('../../../shared/database/connection');
const logger = require('../../../shared/utils/logger');

// Tên tính năng sử dụng cho kết nối database
const FEATURE = 'verify';

/**
 * Kiểm tra thông tin xác thực của học sinh
 * @param {string} email - Email của học sinh
 * @param {string} activationCode - Mã kích hoạt
 * @returns {Promise<Object|null>} Thông tin về thành công hoặc thất bại
 */
async function verifyStudent(email, activationCode) {
  try {
    // SQL query để kiểm tra email và mã kích hoạt
    const sql = `
      SELECT
        u.id AS user_id,
        u.email,
        u.fullname,
        ac.id AS activation_code_id,
        ac.code,
        ac.activated_at,
        ac.discord_status,
        ct.discord_role_id,
        c.title AS course_title,
        ct.tier_name,
        o.id AS order_id
      FROM activation_codes ac
      JOIN orders_activation_codes_lnk oac ON ac.id = oac.activation_code_id
      JOIN orders o ON oac.order_id = o.id
      JOIN orders_users_permissions_user_lnk oupl ON o.id = oupl.order_id
      JOIN up_users u ON oupl.user_id = u.id
      JOIN orders_course_lnk ocl ON o.id = ocl.order_id
      JOIN courses c ON ocl.course_id = c.id
      JOIN orders_course_tier_lnk octl ON o.id = octl.order_id
      JOIN course_tiers ct ON octl.course_tier_id = ct.id
      WHERE u.email = ? AND ac.code = ? AND o.payment_status = 'completed'
      LIMIT 1
    `;

    const [rows] = await db.query(sql, [email, activationCode], FEATURE);

    if (rows.length === 0) {
      logger.info(`Không tìm thấy thông tin xác thực cho email: ${email}, mã: ${activationCode}`);
      return null;
    }

    return rows[0];
  } catch (error) {
    logger.error('Lỗi kiểm tra thông tin xác thực:', {
      message: error.message,
      stack: error.stack,
      email
    });
    throw error;
  }
}

/**
 * Kiểm tra xem email đã được sử dụng để xác thực trên Discord khác chưa
 * @param {string} email - Email của học sinh
 * @returns {Promise<boolean>} true nếu email đã được sử dụng, false nếu chưa
 */
async function checkEmailUsedByOtherDiscord(email) {
  try {
    const sql = `
      SELECT COUNT(*) as count
      FROM up_users
      WHERE email = ? AND user_discord_id IS NOT NULL AND user_discord_id != ''
    `;

    const [rows] = await db.query(sql, [email], FEATURE);

    return rows[0].count > 0;
  } catch (error) {
    logger.error('Lỗi kiểm tra email đã sử dụng:', {
      message: error.message,
      stack: error.stack,
      email
    });
    throw error;
  }
}

/**
 * Lấy tất cả các khóa học mà học sinh đã mua (bao gồm cả khóa đang xác thực)
 * @param {string} email - Email của học sinh
 * @returns {Promise<Array>} Danh sách các khóa học và Discord role ID tương ứng
 */
async function getStudentCourses(email) {
  try {
    const sql = `
      SELECT
        c.id AS course_id,
        c.title AS course_name,
        ct.discord_role_id,
        ct.tier_name,
        ac.id AS activation_code_id,
        ac.code,
        ac.discord_status,
        o.id AS order_id
      FROM up_users u
      JOIN orders_users_permissions_user_lnk oupl ON u.id = oupl.user_id
      JOIN orders o ON oupl.order_id = o.id
      JOIN orders_course_lnk ocl ON o.id = ocl.order_id
      JOIN courses c ON ocl.course_id = c.id
      JOIN orders_course_tier_lnk octl ON o.id = octl.order_id
      JOIN course_tiers ct ON octl.course_tier_id = ct.id
      JOIN orders_activation_codes_lnk oac ON o.id = oac.order_id
      JOIN activation_codes ac ON oac.activation_code_id = ac.id
      WHERE u.email = ? AND o.payment_status = 'completed'
    `;

    const [rows] = await db.query(sql, [email], FEATURE);

    if (rows.length === 0) {
      logger.info(`Không tìm thấy khóa học nào cho email: ${email}`);
      return [];
    }

    return rows;
  } catch (error) {
    logger.error('Lỗi lấy danh sách khóa học của học sinh:', {
      message: error.message,
      stack: error.stack,
      email
    });
    throw error;
  }
}

/**
 * Đánh dấu mã kích hoạt đã được sử dụng
 * @param {number} activationCodeId - ID của mã kích hoạt
 * @param {string} discordUserId - Discord user ID của học sinh
 * @param {string} discordUsername - Tên người dùng Discord của học sinh
 * @param {string} parentsPhone - Số điện thoại phụ huynh
 * @returns {Promise<boolean>} Kết quả cập nhật
 */
async function markActivationAsUsed(activationCodeId, discordUserId, discordUsername, parentsPhone) {
  try {
    // Bắt đầu transaction
    const connection = await db.getPool(FEATURE).getConnection();
    await connection.beginTransaction();

    try {
      // Thiết lập múi giờ Việt Nam cho session này
      await connection.query("SET time_zone = '+07:00'");
      
      // Lấy thông tin user từ activation code
      const [userInfo] = await connection.query(`
        SELECT u.id, u.email
        FROM activation_codes ac
        JOIN orders_activation_codes_lnk oac ON ac.id = oac.activation_code_id
        JOIN orders o ON oac.order_id = o.id
        JOIN orders_users_permissions_user_lnk oupl ON o.id = oupl.order_id
        JOIN up_users u ON oupl.user_id = u.id
        WHERE ac.id = ?
      `, [activationCodeId]);

      if (userInfo.length === 0) {
        logger.warn(`Không thể tìm thấy thông tin người dùng cho activation_code_id: ${activationCodeId}`);
        await connection.rollback();
        connection.release();
        return false;
      }

      // Cập nhật trạng thái và thời gian kích hoạt mã
      const [updateActivationResult] = await connection.query(`
        UPDATE activation_codes
        SET activated_at = NOW(), discord_status = '1'
        WHERE id = ? AND (discord_status = '0' OR discord_status IS NULL)
      `, [activationCodeId]);

      // Kiểm tra xem có cập nhật được không
      if (updateActivationResult.affectedRows === 0) {
        logger.warn(`Không thể cập nhật trạng thái activation_code_id: ${activationCodeId}, có thể đã được kích hoạt`);
        await connection.rollback();
        connection.release();
        return false;
      }

      // Cập nhật Discord ID và SĐT phụ huynh cho user
      // Cập nhật SĐT phụ huynh chỉ khi nó được cung cấp và user_discord_id chưa được set (hoặc đang được set trong cùng transaction này)
      // Điều này tránh việc cập nhật SĐT phụ huynh cho một user đã xác thực trước đó chỉ vì mã này tình cờ liên kết với họ
      let updateUserSQL = `UPDATE up_users SET user_discord_id = ?`;
      const updateUserParams = [discordUserId];

      if (parentsPhone) {
        updateUserSQL += `, parents_phone = ?`;
        updateUserParams.push(parentsPhone);
      }

      updateUserSQL += ` WHERE id = ? AND (user_discord_id IS NULL OR user_discord_id = '' OR user_discord_id = ?)`;
      updateUserParams.push(userInfo[0].id, discordUserId);

      const [updateUserResult] = await connection.query(updateUserSQL, updateUserParams);
      
      // Ghi log nếu không có user nào được cập nhật (có thể do user_discord_id đã tồn tại với giá trị khác)
      // Hoặc SĐT phụ huynh không được cập nhật vì một lý do nào đó (ít khả năng hơn nếu query đúng)
      if (updateUserResult.affectedRows === 0) {
        logger.warn(`Không thể cập nhật user_discord_id và/hoặc parents_phone cho user_id: ${userInfo[0].id} liên kết với activation_code_id: ${activationCodeId}. Có thể user_discord_id đã được set bởi một Discord ID khác.`);
        // Không rollback ở đây nếu việc kích hoạt mã vẫn thành công, nhưng cần ghi log rõ ràng.
        // Quyết định rollback hay không tùy thuộc vào yêu cầu nghiệp vụ: có bắt buộc SĐT phụ huynh phải được cập nhật không.
        // Hiện tại, chỉ log warning và tiếp tục.
      } else {
        logger.info(`Đã cập nhật user_id: ${userInfo[0].id} với discord_id: ${discordUserId}` + (parentsPhone ? ` và SĐT phụ huynh: ${parentsPhone}` : ''));
      }

      // Commit transaction
      await connection.commit();
      connection.release();

      return true;
    } catch (error) {
      // Nếu có lỗi, rollback transaction
      await connection.rollback();
      connection.release();
      throw error;
    }
  } catch (error) {
    logger.error('Lỗi đánh dấu mã kích hoạt đã sử dụng:', {
      message: error.message,
      stack: error.stack,
      activationCodeId,
      discordUserId,
      parentsPhone
    });
    throw error;
  }
}

module.exports = {
  verifyStudent,
  markActivationAsUsed,
  getStudentCourses,
  checkEmailUsedByOtherDiscord
};