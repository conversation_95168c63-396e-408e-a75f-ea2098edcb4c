/**
 * @file verify_service.js
 * @description Service xử lý logic nghiệp vụ cho tính năng xác thực (verify)
 * <AUTHOR> Assistant
 * @date 2025-05-09
 */

const { EmbedBuilder, ButtonBuilder, ButtonStyle, ActionRowBuilder } = require('discord.js');
const verifyModel = require('../models/verify_model');
const logger = require('../../../shared/utils/logger');
const config = require('../../../core/config');

/**
 * G<PERSON>i thông báo xác thực thành công đến channel admin
 * @param {Object} guild - Discord guild object
 * @param {Object} user - Discord user object
 * @param {string} studentName - Tên học sinh
 * @param {string} email - Email của học sinh
 * @param {Array} courses - Danh sách các khóa học đã được cấp quyền
 * @returns {Promise<boolean>} Kết qu<PERSON> gửi thông báo
 */
async function sendAdminNotification(guild, user, studentName, email, courses) {
  try {
    // Lấy channel ID từ cấu hình
    const adminChannelId = config.channels.adminNotificationChannelId;

    if (!adminChannelId) {
      logger.info('ADMIN_NOTIFICATION_CHANNEL_ID không được cấu hình, bỏ qua việc gửi thông báo đến admin');
      return true; // Trả về true để không gây lỗi
    }

    // Lấy channel từ guild
    const adminChannel = await guild.channels.fetch(adminChannelId);

    if (!adminChannel) {
      logger.warn(`Không tìm thấy channel với ID: ${adminChannelId}`);
      return false;
    }

    // Tạo embed thông báo
    const embed = new EmbedBuilder()
      .setTitle('🔔 Học sinh xác thực thành công')
      .setColor(0x00FF00)
      .setTimestamp()
      .setThumbnail(user.displayAvatarURL({ dynamic: true }))
      .addFields(
        { name: 'Tên học sinh', value: studentName || 'Không có thông tin', inline: true },
        { name: 'Discord Tag', value: user.tag, inline: true },
        { name: 'Discord ID', value: user.id, inline: true },
        { name: 'Email', value: email, inline: false },
        { name: 'Thời gian xác thực', value: new Date().toLocaleString('vi-VN'), inline: false },
        {
          name: 'Các khóa học đã cấp quyền',
          value: courses.length > 0 ? courses.map(course => `• ${course}`).join('\n') : 'Không có khóa học nào'
        }
      )
      .setFooter({ text: 'Hệ thống xác thực Ông Ba Dạy Hoá' });

    // Gửi thông báo đến channel admin
    await adminChannel.send({ embeds: [embed] });

    logger.info(`Đã gửi thông báo xác thực thành công của ${user.tag} đến channel admin`);
    return true;
  } catch (error) {
    logger.error('Lỗi khi gửi thông báo xác thực đến channel admin:', {
      message: error.message,
      stack: error.stack,
      userId: user.id
    });
    return false;
  }
}

/**
 * Xác thực học sinh dựa vào email và mã kích hoạt
 * @param {string} email - Email của học sinh
 * @param {string} activationCode - Mã kích hoạt
 * @param {Object} user - Discord user object (từ interaction.user)
 * @param {Object} member - Discord member object (từ interaction.member)
 * @param {Object} guild - Discord guild object
 * @param {string} parentsPhone - Số điện thoại phụ huynh
 * @returns {Promise<Object>} Kết quả xác thực và thông tin roles
 */
async function verifyStudent(email, activationCode, user, member, guild, parentsPhone) {
  try {
    logger.info(`Bắt đầu quá trình xác thực cho email: ${email}, SĐT phụ huynh: ${parentsPhone}`);

    // Validate input
    if (!email || !activationCode || !parentsPhone) {
      logger.warn('Thiếu thông tin email, mã kích hoạt hoặc SĐT phụ huynh');
      return {
        success: false,
        message: 'Vui lòng cung cấp đầy đủ email, mã kích hoạt và số điện thoại phụ huynh.'
      };
    }

    // Validate cơ bản định dạng email để ngăn ngừa SQL injection
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      logger.warn('Định dạng email không hợp lệ');
      return {
        success: false,
        message: 'Định dạng email không hợp lệ. Vui lòng kiểm tra lại.'
      };
    }

    // Kiểm tra thông tin từ database
    const verificationInfo = await verifyModel.verifyStudent(email, activationCode);

    // Kiểm tra nếu không tìm thấy thông tin
    if (!verificationInfo) {
      logger.warn(`Không tìm thấy thông tin khớp cho email: ${email} và mã: ${activationCode}`);
      return {
        success: false,
        message: 'Email hoặc mã kích hoạt không chính xác. Vui lòng kiểm tra lại hoặc liên hệ hỗ trợ.'
      };
    }

    // Kiểm tra nếu mã đã được kích hoạt
    if (verificationInfo.activated_at || verificationInfo.discord_status === '1') {
      logger.warn(`Mã kích hoạt đã được sử dụng: ${activationCode}`);
      return {
        success: false,
        message: 'Mã kích hoạt này đã được sử dụng. Vui lòng liên hệ hỗ trợ nếu bạn cần trợ giúp.'
      };
    }

    // Kiểm tra xem email đã được sử dụng bởi một tài khoản Discord khác chưa
    const isEmailUsed = await verifyModel.checkEmailUsedByOtherDiscord(email);
    if (isEmailUsed) {
      logger.warn(`Email ${email} đã được sử dụng cho một tài khoản Discord khác`);
      return {
        success: false,
        message: 'Email này đã được liên kết với một tài khoản Discord khác. Vui lòng liên hệ hỗ trợ nếu đây là tài khoản của bạn.'
      };
    }

    // Lấy tất cả các khóa học mà học sinh đã mua
    const courses = await verifyModel.getStudentCourses(email);

    if (courses.length === 0) {
      logger.warn(`Không tìm thấy khóa học nào cho email: ${email}`);
      return {
        success: false,
        message: 'Không tìm thấy khóa học nào được liên kết với email này. Vui lòng liên hệ hỗ trợ.'
      };
    }

    // Đánh dấu mã kích hoạt đã được sử dụng và cập nhật SĐT phụ huynh
    const updateResult = await verifyModel.markActivationAsUsed(
      verificationInfo.activation_code_id,
      user.id,
      user.tag,
      parentsPhone
    );

    if (!updateResult) {
      logger.error(`Không thể cập nhật trạng thái kích hoạt cho: ${verificationInfo.activation_code_id}`);
      return {
        success: false,
        message: 'Có lỗi xảy ra khi xác thực. Vui lòng thử lại sau hoặc liên hệ admin.'
      };
    }

    // Gán các roles tương ứng với các khóa học
    const assignedRoles = [];
    const failedRoles = [];

    for (const course of courses) {
      if (course.discord_role_id) {
        try {
          const role = await guild.roles.fetch(course.discord_role_id);

          if (role) {
            // Kiểm tra xem đã có role chưa
            if (!member.roles.cache.has(role.id)) {
              await member.roles.add(role);
              // Thêm tên khóa học và tier vào danh sách
              const courseInfo = `${course.course_name} (${course.tier_name || 'Standard'})`;
              assignedRoles.push(courseInfo);
            } else {
              logger.info(`Học sinh ${user.tag} đã có role ${role.name} cho khóa học ${course.course_name}`);
              const courseInfo = `${course.course_name} (${course.tier_name || 'Standard'})`;
              assignedRoles.push(courseInfo);
            }
          } else {
            logger.warn(`Không tìm thấy role ID: ${course.discord_role_id} cho khóa học: ${course.course_name}`);
            failedRoles.push(`${course.course_name} (${course.tier_name || 'Standard'})`);
          }
        } catch (error) {
          logger.error(`Lỗi khi thêm role cho khóa học ${course.course_name}:`, {
            message: error.message,
            stack: error.stack,
            roleId: course.discord_role_id
          });
          let reason = 'Lỗi không xác định';
          if (error.code === 50013) { // Missing Permissions
            reason = 'Bot thiếu quyền';
            // Ghi log cụ thể hơn cho lỗi thiếu quyền
            logger.error(`Bot không có quyền thêm role ${course.discord_role_id} (${fetchedRole ? fetchedRole.name : 'Không rõ tên'}) cho khóa học ${course.course_name}. Kiểm tra quyền của bot trên server và channel.`);
          } else if (error.code === 10011) { // Unknown Role
            reason = 'Role không xác định';
             // Ghi log cụ thể hơn cho lỗi role không xác định
            logger.warn(`Role ID ${course.discord_role_id} không xác định (unknown) cho khóa học ${course.course_name}. Role có thể đã bị xóa hoặc ID sai.`, error.message);
          }
          failedRoles.push(`${course.course_name} (${course.tier_name || 'Standard'}) - ${reason}`);
        }
      } else {
        logger.warn(`Không có discord_role_id cho khóa học: ${course.course_name}`);
        failedRoles.push(`${course.course_name} (${course.tier_name || 'Standard'})`);
      }
    }

    // Thay đổi nickname của thành viên nếu có tên đầy đủ từ database
    let nicknameUpdated = false;
    if (verificationInfo.fullname && member.manageable) {
      try {
        // Lưu nickname hiện tại để ghi log
        const currentNickname = member.nickname || user.username;

        // Đổi nickname thành tên đầy đủ từ database
        await member.setNickname(verificationInfo.fullname);

        logger.info(`Đã đổi nickname của ${user.tag} từ "${currentNickname}" thành "${verificationInfo.fullname}"`);
        nicknameUpdated = true;
      } catch (error) {
        if (error.code === 50013) { // Missing Permissions
          logger.error(`Bot không có quyền đổi nickname cho ${user.tag} thành "${verificationInfo.fullname}". Kiểm tra quyền của bot.`, {
            message: error.message,
            stack: error.stack
          });
        } else if (error.code === 50035) { // Invalid Form Body (e.g., nickname too long)
          logger.warn(`Nickname "${verificationInfo.fullname}" không hợp lệ (quá dài hoặc chứa ký tự không cho phép) cho ${user.tag}.`, {
            message: error.message,
            stack: error.stack
          });
        } else {
          logger.error(`Không thể đổi nickname của ${user.tag} thành "${verificationInfo.fullname}" (Lỗi không xác định):`, {
            message: error.message,
            stack: error.stack
          });
        }
      }
    } else if (!member.manageable) {
      logger.warn(`Không thể đổi nickname cho ${user.tag} vì bot không có đủ quyền`);
    }

    // Gửi thông báo cho channel admin, ghi log nếu thất bại nhưng không ảnh hưởng kết quả
    const adminNotified = await sendAdminNotification(
      guild,
      user,
      verificationInfo.fullname,
      email,
      assignedRoles
    );
    if (!adminNotified) {
      logger.error('Lỗi khi gửi thông báo admin', { user: user.id, email });
    }

    // Tạo thông báo kết quả
    let resultMessage = 'Xác thực thành công!';

    if (assignedRoles.length > 0) {
      resultMessage += `\nBạn đã được cấp quyền truy cập vào khóa học: ${assignedRoles.join(', ')}`;
    }

    if (nicknameUpdated) {
      resultMessage += `\nTên hiển thị của bạn đã được cập nhật thành "${verificationInfo.fullname}"`;
    }

    if (failedRoles.length > 0) {
      resultMessage += `\n⚠️ Không thể cấp quyền cho khóa học: ${failedRoles.join(', ')}. Vui lòng liên hệ admin.`;
    }

    return {
      success: true,
      message: resultMessage,
      // Trả về danh sách tất cả các khóa học theo định dạng Course (Tier)
      courses: courses.map(course => `${course.course_name} (${course.tier_name || 'Standard'})`),
      failedCourses: failedRoles,
      studentName: verificationInfo.fullname,
      nicknameUpdated: nicknameUpdated
    };
  } catch (error) {
    logger.error('Lỗi khi xác thực học sinh:', {
      message: error.message,
      stack: error.stack,
      email
    });

    return {
      success: false,
      message: 'Đã xảy ra lỗi trong quá trình xác thực. Vui lòng thử lại sau hoặc liên hệ admin.',
      error: error.message
    };
  }
}

/**
 * Tạo embed hiển thị thông báo kết quả xác thực
 * @param {boolean} success - Kết quả xác thực có thành công không
 * @param {string} message - Thông điệp kết quả
 * @param {Array} courses - Các khóa học đã cấp quyền thành công (nếu có)
 * @param {string} studentName - Tên học sinh (nếu có)
 * @returns {Object} Object chứa embed và action row với button hỗ trợ
 */
function createVerificationResultEmbed(success, message, courses = [], studentName = '') {
  const embed = new EmbedBuilder()
    .setTitle(success ? '✅ Xác thực Học sinh Ông Ba Dạy Hoá' : '❌ Xác thực không thành công')
    .setDescription(success
      ? `Nếu bạn đã mua khóa học trên website của Ông Ba Dạy Hoá, bạn đã được xác thực thành công và nhận các role Discord tương ứng.`
      : message)
    .setColor(success ? 0x3498db : 0xff0000) // Màu xanh cho thành công, đỏ cho thất bại
    .setTimestamp()
    .setFooter({
      text: 'Ông Ba Dạy Hoá - Học hoá dễ như ăn kẹo'
    });

  if (success) {
    // Thêm thông tin học sinh nếu có
    if (studentName) {
      embed.addFields({
        name: 'Thông tin học sinh',
        value: `**${studentName}**`
      });
    }

    // Thêm danh sách khóa học nếu có
    if (courses && courses.length > 0) {
      embed.addFields({
        name: '📚 Các khóa học của bạn',
        value: courses.map(course => `• ${course}`).join('\n')
      });
    }

    // Thêm hướng dẫn sau khi xác thực
    embed.addFields({
      name: '📋 Bạn cần làm gì tiếp theo?',
      value: '• Kiểm tra thanh bên trái để xem các kênh mới\n• Đọc các quy tắc và hướng dẫn trong mỗi kênh\n• Nếu cần hỗ trợ, hãy liên hệ với quản trị viên'
    });
  }

  // Tạo nút hỗ trợ qua Zalo
  /**
   * Tạo nút liên hệ hỗ trợ qua Zalo
   * Giúp người dùng dễ dàng liên hệ với quản trị viên khi cần hỗ trợ
   * Nút này được hiển thị ở cả thông báo xác thực thành công và thất bại
   */
  const supportButton = new ButtonBuilder()
    .setLabel('Hỗ trợ qua Zalo')
    .setStyle(ButtonStyle.Link)
    .setURL('https://zalo.me/0828949479')
    .setEmoji('💬');

  const actionRow = new ActionRowBuilder().addComponents(supportButton);

  return {
    embed: embed,
    actionRow: actionRow
  };
}

module.exports = {
  verifyStudent,
  createVerificationResultEmbed,
  sendAdminNotification
};