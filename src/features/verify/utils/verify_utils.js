/**
 * @file verify_utils.js
 * @description Các hàm tiện ích cho tính năng xác thực (verify)
 * <AUTHOR> Assistant
 * @date 2025-05-09
 */

const { ActionRowBuilder, ModalBuilder, TextInputBuilder, TextInputStyle, ButtonBuilder, ButtonStyle } = require('discord.js');

/**
 * Tạo button xác thực cho kênh
 * @returns {ActionRowBuilder} Action row chứa button xác thực và button hỗ trợ
 * @description Tạo một hàng action row với nút "Xác thực tài khoản" và nút "Hỗ trợ qua Zalo"
 *              Nút hỗ trợ Zalo giúp học sinh dễ dàng liên hệ với quản trị viên khi cần trợ giúp
 *              trong quá trình xác thực, tăng tính thân thiện với người dùng
 */
function createVerifyButton() {
  // Button xác thực
  const verifyButton = new ButtonBuilder()
    .setCustomId('verify:button:verify')
    .setLabel('<PERSON><PERSON><PERSON> thực tài khoản')
    .setStyle(ButtonStyle.Primary)
    .setEmoji('✅');

  // Button hỗ trợ qua Zalo
  const supportButton = new ButtonBuilder()
    .setLabel('Hỗ trợ qua Zalo')
    .setStyle(ButtonStyle.Link)
    .setURL('https://zalo.me/0828949479')
    .setEmoji('💬');

  // Tạo action row chứa cả hai nút
  return new ActionRowBuilder().addComponents(verifyButton, supportButton);
}

/**
 * Tạo modal để học sinh nhập thông tin xác thực
 * @returns {ModalBuilder} Modal nhập thông tin xác thực
 */
function createVerifyModal() {
  // Tạo các trường input
  const emailInput = new TextInputBuilder()
    .setCustomId('email_input')
    .setLabel('Email đã dùng để mua khóa học')
    .setPlaceholder('<EMAIL>')
    .setStyle(TextInputStyle.Short)
    .setRequired(true)
    .setMinLength(5)
    .setMaxLength(100);

  const codeInput = new TextInputBuilder()
    .setCustomId('code_input')
    .setLabel('Mã kích hoạt')
    .setPlaceholder('ABC1234 (7 ký tự A-Z, 0-9 in hoa)')
    .setStyle(TextInputStyle.Short)
    .setRequired(true)
    .setMinLength(7)
    .setMaxLength(7);

  const parentsPhoneInput = new TextInputBuilder()
    .setCustomId('parents_phone_input')
    .setLabel('Số điện thoại phụ huynh (10 số)')
    .setPlaceholder('VD: 09XXXXXXXX')
    .setStyle(TextInputStyle.Short)
    .setRequired(true)
    .setMinLength(10)
    .setMaxLength(10);

  // Tạo các action row chứa các trường input
  const emailRow = new ActionRowBuilder().addComponents(emailInput);
  const codeRow = new ActionRowBuilder().addComponents(codeInput);
  const parentsPhoneRow = new ActionRowBuilder().addComponents(parentsPhoneInput);

  // Tạo modal
  const modal = new ModalBuilder()
    .setCustomId('verify:modal:submit')
    .setTitle('Xác thực tài khoản')
    .addComponents(emailRow, codeRow, parentsPhoneRow);

  return modal;
}

/**
 * Tạo thông báo thành công cho người dùng
 * @param {string} studentName - Tên học sinh (nếu có)
 * @returns {string} Thông báo thành công
 */
function getSuccessMessage(studentName = '') {
  let message = `
## ✅ Xác thực thành công!

`;

  if (studentName) {
    message += `Chào mừng **${studentName}**! `;
  }

  message += `Chúc mừng! Bạn đã xác thực thành công và được cấp quyền truy cập vào các kênh học tập tương ứng với khóa học đã mua.

- Kiểm tra thanh bên trái để xem các kênh mới.
- Đọc các quy tắc và hướng dẫn trong mỗi kênh.
- Nếu cần hỗ trợ, hãy liên hệ với quản trị viên.

Chúc bạn học tập hiệu quả!
`;

  return message;
}

/**
 * Tạo thông báo lỗi xác thực
 * @param {string} errorMessage - Thông báo lỗi cụ thể
 * @returns {string} Thông báo lỗi đầy đủ
 */
function getErrorMessage(errorMessage) {
  return `
## ❌ Xác thực không thành công

${errorMessage}

Nếu bạn cần hỗ trợ, vui lòng liên hệ với quản trị viên qua Zalo: https://zalo.me/0828949479
`;
}

module.exports = {
  createVerifyButton,
  createVerifyModal,
  getSuccessMessage,
  getErrorMessage
};