/**
 * @file index.js
 * @description Điểm vào chính của bot, khởi động bot và liên kết các thành phần
 * <AUTHOR> Assistant
 * @date 2025-05-19
 */

// Thiết lập múi giờ mặc định cho Việt Nam
process.env.TZ = 'Asia/Ho_Chi_Minh';

// Tải các module cần thiết
const config = require('./core/config');
const { createClient } = require('./core/client');
const { loadCommands, loadEvents, registerCommands, loadFeatures } = require('./core/bot');
const logger = require('./shared/utils/logger');
const { Collection } = require('discord.js');
const db = require('./shared/database/connection');

// Log thông tin khởi động
logger.info('Khởi động bot Ông Ba Dạy Hoá...');
logger.debug(`Múi giờ: ${new Date().toString()}`);

// Hàm chính để khởi động bot
async function main() {
  try {
    // Kiểm tra các biến môi trường bắt buộc
    config.checkRequiredEnvs();
    
    // Tạo Discord client
    logger.info('Khởi tạo Discord client');
    const client = createClient();
    
    // Khởi tạo các collections cho handlers
    client.buttonHandlers = new Collection();
    client.selectMenuHandlers = new Collection();
    client.modalHandlers = new Collection();
    
    // Kiểm tra kết nối database
    try {
      logger.info('Kiểm tra kết nối đến MySQL database');
      await db.testConnection();
    } catch (dbError) {
      logger.error('Không thể kết nối đến database. Bot sẽ tiếp tục chạy nhưng các tính năng sử dụng database có thể không hoạt động.', {
        message: dbError.message,
        stack: dbError.stack
      });
    }
    
    // Load các commands
    logger.info('Loading commands...');
    const commandsToRegister = loadCommands(client);
    
    // Load các events
    logger.info('Loading events...');
    loadEvents(client);
    
    // Load và khởi tạo các tính năng
    logger.info('Loading features...');
    loadFeatures(client);
    
    // Đăng nhập vào Discord
    logger.info('Đăng nhập vào Discord...');
    await client.login(config.bot.token);
    
    // Đăng ký slash commands với Discord API sau khi đăng nhập thành công
    logger.info('Đăng ký slash commands...');
    await registerCommands(commandsToRegister);
    
    logger.info('Bot khởi động thành công!');
  } catch (error) {
    logger.error('Lỗi khởi động bot:', { 
      message: error.message, 
      stack: error.stack 
    });
    process.exit(1);
  }
}

// Bắt đầu chương trình
main();

// Xử lý tín hiệu tắt bot
process.on('SIGINT', () => {
  logger.info('Nhận tín hiệu SIGINT - Tắt bot...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  logger.info('Nhận tín hiệu SIGTERM - Tắt bot...');
  process.exit(0);
}); 