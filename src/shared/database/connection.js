/**
 * @file connection.js
 * @description Module quản lý kết nối tới cơ sở dữ liệu MySQL cho từng tính năng
 * <AUTHOR> Assistant
 * @date 2025-05-19
 */

const mysql = require('mysql2/promise');
const config = require('../../core/config');
const logger = require('../utils/logger');

/**
 * Quản lý các pool kết nối MySQL cho từng tính năng
 * @type {Object.<string, mysql.Pool>}
 */
const pools = {};

// Tính năng mặc định sử dụng cho database chung
const DEFAULT_FEATURE = 'verify';

/**
 * Khởi tạo kết nối pool tới MySQL cho một tính năng cụ thể
 * @param {string} feature - Tên tính năng cần kết nối (ví dụ: 'verify')
 * @returns {mysql.Pool} MySQL connection pool
 */
function initPool(feature = DEFAULT_FEATURE) {
  try {
    if (!config.database[feature]) {
      throw new Error(`Không tìm thấy cấu hình database cho tính năng: ${feature}`);
    }

    if (pools[feature]) {
      logger.info(`Database pool cho tính năng ${feature} đã tồn tại, trả về instance hiện tại`);
      return pools[feature];
    }
    
    logger.info(`Khởi tạo kết nối pool tới MySQL database cho tính năng ${feature}`);
    
    // Tạo cấu hình với timezone cho Việt Nam
    const poolConfig = {
      ...config.database[feature],
      timezone: '+07:00', // Múi giờ Việt Nam (UTC+7)
    };
    
    pools[feature] = mysql.createPool(poolConfig);
    
    // Test kết nối
    testConnection(feature);
    
    return pools[feature];
  } catch (error) {
    logger.error(`Lỗi khởi tạo kết nối pool tới database cho tính năng ${feature}:`, { 
      message: error.message, 
      stack: error.stack 
    });
    throw error;
  }
}

/**
 * Kiểm tra kết nối tới database cho một tính năng cụ thể
 * @param {string} feature - Tên tính năng cần kiểm tra kết nối
 * @returns {Promise<boolean>} Kết quả kiểm tra
 */
async function testConnection(feature = DEFAULT_FEATURE) {
  if (!pools[feature]) {
    initPool(feature);
  }

  try {
    const connection = await pools[feature].getConnection();
    logger.info(`Kết nối MySQL cho tính năng ${feature} thành công`);
    
    // Kiểm tra thông tin kết nối
    const [rows] = await connection.query('SELECT VERSION() as version');
    logger.info(`Kết nối tới MySQL server phiên bản: ${rows[0].version} (${feature})`);
    
    connection.release();
    return true;
  } catch (error) {
    logger.error(`Lỗi kết nối tới MySQL database cho tính năng ${feature}:`, { 
      message: error.message, 
      stack: error.stack,
      host: config.database[feature].host,
      database: config.database[feature].database,
      user: config.database[feature].user
    });
    throw error;
  }
}

/**
 * Đóng pool kết nối MySQL cho một tính năng hoặc tất cả các tính năng
 * @param {string} [feature] - Tên tính năng cần đóng kết nối, nếu không cung cấp sẽ đóng tất cả
 * @returns {Promise<void>}
 */
async function closePool(feature) {
  try {
    if (feature) {
      if (pools[feature]) {
        logger.info(`Đóng kết nối pool MySQL cho tính năng ${feature}`);
        await pools[feature].end();
        delete pools[feature];
      }
    } else {
      // Đóng tất cả các pool
      const promises = [];
      for (const feat in pools) {
        logger.info(`Đóng kết nối pool MySQL cho tính năng ${feat}`);
        promises.push(pools[feat].end());
      }
      await Promise.all(promises);
      Object.keys(pools).forEach(key => delete pools[key]);
    }
  } catch (error) {
    logger.error('Lỗi đóng kết nối pool MySQL:', { 
      message: error.message, 
      stack: error.stack 
    });
    throw error;
  }
}

// Khởi tạo pool mặc định cho tính năng verify khi module được import
initPool(DEFAULT_FEATURE);

// Xử lý đóng kết nối khi chương trình kết thúc
process.on('SIGINT', async () => {
  logger.info('Nhận tín hiệu SIGINT, đóng kết nối database');
  await closePool();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  logger.info('Nhận tín hiệu SIGTERM, đóng kết nối database');
  await closePool();
  process.exit(0);
});

module.exports = {
  getPool: (feature = DEFAULT_FEATURE) => pools[feature] || initPool(feature),
  query: async (sql, params, feature = DEFAULT_FEATURE) => {
    if (!pools[feature]) {
      initPool(feature);
    }
    return pools[feature].query(sql, params);
  },
  closePool,
  testConnection
}; 