/**
 * @file logger.js
 * @description Module quản lý việc ghi log trong ứng dụng
 * <AUTHOR> Assistant
 * @date 2025-05-19
 */

const { createLogger, format, transports } = require('winston');
const { combine, timestamp, printf, colorize } = format;
const config = require('../../core/config');
const path = require('path');
const fs = require('fs');

// <PERSON><PERSON><PERSON> b<PERSON><PERSON> thư mục logs tồn tại
const logsDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Format cho log
const logFormat = printf(({ level, message, timestamp, ...meta }) => {
  let logMessage = `${timestamp} [${level}]: ${message}`;
  
  // Thêm thông tin stack trace nếu có
  if (meta.stack) {
    logMessage += `\nStack: ${meta.stack}`;
    delete meta.stack;
  }
  
  // Thêm metadata khác nếu có
  const remainingMeta = Object.keys(meta).length > 0 
    ? `\nData: ${JSON.stringify(meta)}` 
    : '';
  
  return logMessage + remainingMeta;
});

/**
 * Logger chính của ứng dụng
 */
const logger = createLogger({
  level: config.app.logLevel,
  format: combine(
    timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
    logFormat
  ),
  transports: [
    // Log thông thường vào console
    new transports.Console({
      format: combine(
        colorize(),
        timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
        logFormat
      )
    }),
    
    // Log tất cả vào file
    new transports.File({ 
      filename: path.join(logsDir, 'combined.log'),
      maxsize: 5242880, // 5MB
      maxFiles: 5
    }),
    
    // Log lỗi vào file riêng
    new transports.File({ 
      filename: path.join(logsDir, 'error.log'),
      level: 'error',
      maxsize: 5242880, // 5MB
      maxFiles: 5
    })
  ],
  exitOnError: false
});

/**
 * Bắt các lỗi chưa được xử lý và ghi log
 */
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', { message: error.message, stack: error.stack });
  // Trong môi trường production, có thể xem xét việc thoát ứng dụng sau khi log
  if (config.app.nodeEnv === 'production') {
    // Cho phép log được ghi ra trước khi thoát
    setTimeout(() => {
      process.exit(1);
    }, 1000);
  }
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection:', { 
    reason: reason instanceof Error ? reason.message : reason,
    stack: reason instanceof Error ? reason.stack : 'No stack trace available'
  });
});

module.exports = logger; 