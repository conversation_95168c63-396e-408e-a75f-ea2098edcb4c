/**
 * @file verify_command.test.js
 * @description Unit tests cho verify command
 * <AUTHOR> Assistant
 * @date 2025-05-09
 */

// Mock các dependency
jest.mock('discord.js', () => ({
  SlashCommandBuilder: jest.fn().mockImplementation(() => ({
    setName: jest.fn().mockImplementation(function(name) {
      this.name = name;
      return this;
    }),
    setDescription: jest.fn().mockImplementation(function(desc) {
      this.description = desc;
      return this;
    }),
    setDefaultMemberPermissions: jest.fn().mockReturnThis()
  })),
  EmbedBuilder: jest.fn().mockImplementation(() => ({
    setTitle: jest.fn().mockReturnThis(),
    setDescription: jest.fn().mockReturnThis(),
    setColor: jest.fn().mockReturnThis(),
    setFooter: jest.fn().mockReturnThis(),
    setTimestamp: jest.fn().mockReturnThis()
  })),
  PermissionFlagsBits: {
    Administrator: 8
  }
}));

jest.mock('../../../../src/shared/utils/logger', () => ({
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn()
}));

jest.mock('../../../../src/core/config', () => ({
  permissions: {
    verifyAdminId: 'admin123'
  }
}));

jest.mock('../../../../src/features/verify/utils/verify_utils', () => ({
  createVerifyButton: jest.fn().mockReturnValue('mockActionRow')
}));

// Import module cần test
const verifyCommand = require('../../../../src/features/verify/commands/verify_command');
const logger = require('../../../../src/shared/utils/logger');
const verifyUtils = require('../../../../src/features/verify/utils/verify_utils');

describe('Verify Command', () => {
  // Tạo mock cho interaction
  let mockInteraction;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Tạo mock interaction
    mockInteraction = {
      user: {
        id: 'admin123',
        tag: 'admin#1234'
      },
      reply: jest.fn().mockResolvedValue(true),
      channel: {
        name: 'verify-channel',
        send: jest.fn().mockResolvedValue(true)
      }
    };
  });

  describe('data', () => {
    it('should have correct command name and description', () => {
      // Assert
      expect(verifyCommand.data).toBeDefined();
      // Kiểm tra tên và mô tả của command
      expect(verifyCommand.data.name).toBe('verify');
      expect(verifyCommand.data.description).toContain('Tạo button xác thực');
    });
  });

  describe('execute', () => {
    it('should create verify button when executed by admin', async () => {
      // Act
      await verifyCommand.execute(mockInteraction);

      // Assert
      expect(mockInteraction.reply).toHaveBeenCalledWith({
        content: expect.stringContaining('Button xác thực đã được tạo'),
        ephemeral: true
      });

      expect(verifyUtils.createVerifyButton).toHaveBeenCalled();

      expect(mockInteraction.channel.send).toHaveBeenCalledWith({
        embeds: [expect.any(Object)],
        components: ['mockActionRow']
      });

      expect(logger.info).toHaveBeenCalledWith(
        expect.stringContaining('đã tạo button xác thực')
      );
    });

    it('should reject non-admin users', async () => {
      // Arrange
      mockInteraction.user.id = 'user456'; // Not admin

      // Act
      await verifyCommand.execute(mockInteraction);

      // Assert
      expect(mockInteraction.reply).toHaveBeenCalledWith({
        content: expect.stringContaining('❌ Bạn không có quyền sử dụng lệnh này'),
        ephemeral: true
      });

      expect(logger.warn).toHaveBeenCalledWith(
        expect.stringContaining('đã cố gắng sử dụng lệnh verify nhưng không được phép')
      );

      expect(mockInteraction.channel.send).not.toHaveBeenCalled();
    });

    it('should handle errors gracefully', async () => {
      // Arrange
      const error = new Error('Test error');
      mockInteraction.channel.send.mockRejectedValue(error);

      // Act
      await verifyCommand.execute(mockInteraction);

      // Assert
      expect(mockInteraction.reply).toHaveBeenCalledWith({
        content: expect.stringContaining('❌ Đã xảy ra lỗi'),
        ephemeral: true
      });

      expect(logger.error).toHaveBeenCalledWith(
        'Lỗi khi tạo button xác thực:',
        expect.objectContaining({
          message: error.message,
          stack: error.stack
        })
      );
    });
  });
});
