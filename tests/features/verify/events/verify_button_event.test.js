/**
 * @file verify_button_event.test.js
 * @description Unit tests cho verify button event handler
 * <AUTHOR> Assistant
 * @date 2025-05-09
 */

// Mock các dependency
jest.mock('../../../../src/features/verify/utils/verify_utils', () => ({
  createVerifyModal: jest.fn().mockReturnValue('mockModal')
}));

jest.mock('../../../../src/shared/utils/logger', () => ({
  info: jest.fn(),
  error: jest.fn()
}));

jest.mock('discord.js', () => {
  const mockAddComponents = jest.fn().mockReturnThis();

  return {
    ActionRowBuilder: jest.fn().mockImplementation(() => ({
      addComponents: mockAddComponents
    })),
    ButtonBuilder: jest.fn().mockImplementation(() => ({
      setLabel: jest.fn().mockReturnThis(),
      setStyle: jest.fn().mockReturnThis(),
      setURL: jest.fn().mockReturnThis(),
      setEmoji: jest.fn().mockReturnThis()
    })),
    ButtonStyle: {
      Link: 5
    }
  };
});

// Import các module cần test
const verifyUtils = require('../../../../src/features/verify/utils/verify_utils');
const logger = require('../../../../src/shared/utils/logger');

describe('Verify Button Event', () => {
  // Tạo mock client
  let mockClient;

  // Tạo mock interaction
  let mockInteraction;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Tạo mock client
    mockClient = {
      buttonHandlers: {
        set: jest.fn()
      }
    };

    // Tạo mock interaction
    mockInteraction = {
      user: {
        tag: 'user#1234'
      },
      showModal: jest.fn().mockResolvedValue(true),
      replied: false,
      deferred: false,
      reply: jest.fn().mockResolvedValue(true)
    };
  });

  describe('module registration', () => {
    it('should register button handler with client', () => {
      // Act
      const buttonEventHandler = require('../../../../src/features/verify/events/verify_button_event');
      buttonEventHandler(mockClient);

      // Assert
      expect(mockClient.buttonHandlers.set).toHaveBeenCalledWith('verify', expect.any(Function));
      expect(logger.info).toHaveBeenCalledWith(expect.stringContaining('Đã đăng ký handler'));
    });
  });

  describe('handleVerifyButton', () => {
    // Lấy handler function để test trực tiếp
    let handleVerifyButton;

    beforeEach(() => {
      // Lấy handler function từ module
      const buttonEventHandler = require('../../../../src/features/verify/events/verify_button_event');
      buttonEventHandler(mockClient);

      // Lấy function đã được đăng ký
      handleVerifyButton = mockClient.buttonHandlers.set.mock.calls[0][1];
    });

    it('should show modal when verify button is clicked', async () => {
      // Act
      await handleVerifyButton(mockInteraction, 'button', ['verify']);

      // Assert
      expect(logger.info).toHaveBeenCalledWith(expect.stringContaining('đã nhấn button xác thực'));
      expect(verifyUtils.createVerifyModal).toHaveBeenCalled();
      expect(mockInteraction.showModal).toHaveBeenCalledWith('mockModal');
    });

    it('should do nothing for non-verify buttons', async () => {
      // Act
      await handleVerifyButton(mockInteraction, 'button', ['other']);

      // Assert
      expect(verifyUtils.createVerifyModal).not.toHaveBeenCalled();
      expect(mockInteraction.showModal).not.toHaveBeenCalled();
    });

    it('should do nothing for non-button actions', async () => {
      // Act
      await handleVerifyButton(mockInteraction, 'other', ['verify']);

      // Assert
      expect(verifyUtils.createVerifyModal).not.toHaveBeenCalled();
      expect(mockInteraction.showModal).not.toHaveBeenCalled();
    });

    it('should handle errors when showing modal', async () => {
      // Arrange
      const error = new Error('Test error');
      mockInteraction.showModal.mockRejectedValue(error);

      // Act
      await handleVerifyButton(mockInteraction, 'button', ['verify']);

      // Assert
      expect(logger.error).toHaveBeenCalledWith(
        'Lỗi khi xử lý button xác thực:',
        expect.objectContaining({
          message: error.message,
          stack: error.stack
        })
      );
      expect(mockInteraction.reply).toHaveBeenCalledWith(expect.objectContaining({
        content: expect.stringContaining('❌ Đã xảy ra lỗi'),
        ephemeral: true
      }));
    });

    it('should handle errors when replying with error message', async () => {
      // Arrange
      const showModalError = new Error('Show modal error');
      const replyError = new Error('Reply error');

      mockInteraction.showModal.mockRejectedValue(showModalError);
      mockInteraction.reply.mockRejectedValue(replyError);

      // Act
      await handleVerifyButton(mockInteraction, 'button', ['verify']);

      // Assert
      expect(logger.error).toHaveBeenCalledWith(
        'Lỗi khi xử lý button xác thực:',
        expect.objectContaining({
          message: showModalError.message
        })
      );
      expect(logger.error).toHaveBeenCalledWith(
        'Không thể gửi thông báo lỗi:',
        replyError.message
      );
    });

    it('should not reply if interaction is already replied', async () => {
      // Arrange
      const error = new Error('Test error');
      mockInteraction.showModal.mockRejectedValue(error);
      mockInteraction.replied = true;

      // Act
      await handleVerifyButton(mockInteraction, 'button', ['verify']);

      // Assert
      expect(logger.error).toHaveBeenCalled();
      expect(mockInteraction.reply).not.toHaveBeenCalled();
    });

    it('should not reply if interaction is already deferred', async () => {
      // Arrange
      const error = new Error('Test error');
      mockInteraction.showModal.mockRejectedValue(error);
      mockInteraction.deferred = true;

      // Act
      await handleVerifyButton(mockInteraction, 'button', ['verify']);

      // Assert
      expect(logger.error).toHaveBeenCalled();
      expect(mockInteraction.reply).not.toHaveBeenCalled();
    });
  });
});
