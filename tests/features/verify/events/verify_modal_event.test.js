/**
 * @file verify_modal_event.test.js
 * @description Unit tests cho verify modal event handler
 * <AUTHOR> Assistant
 * @date 2025-05-09
 */

// Mock các dependency
jest.mock('../../../../src/features/verify/services/verify_service', () => ({
  verifyStudent: jest.fn(),
  createVerificationResultEmbed: jest.fn()
}));

jest.mock('../../../../src/features/verify/utils/verify_utils', () => ({
  createVerifyModal: jest.fn()
}));

jest.mock('../../../../src/shared/utils/logger', () => ({
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn()
}));

const mockAddComponents = jest.fn().mockReturnThis();
const mockAddFields = jest.fn().mockReturnThis();

jest.mock('discord.js', () => ({
  ButtonBuilder: jest.fn().mockImplementation(() => ({
    setCustomId: jest.fn().mockReturnThis(),
    setLabel: jest.fn().mockReturnThis(),
    setStyle: jest.fn().mockReturnThis(),
    setURL: jest.fn().mockReturnThis(),
    setEmoji: jest.fn().mockReturnThis()
  })),
  ButtonStyle: {
    Primary: 1,
    Link: 5
  },
  ActionRowBuilder: jest.fn().mockImplementation(() => ({
    addComponents: mockAddComponents
  }))
}));

// Import các module cần test
const verifyService = require('../../../../src/features/verify/services/verify_service');
const logger = require('../../../../src/shared/utils/logger');

describe('Verify Modal Event', () => {
  // Tạo mock client
  let mockClient;

  // Tạo mock interaction
  let mockInteraction;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Tạo mock client
    mockClient = {
      modalHandlers: {
        set: jest.fn()
      }
    };

    // Tạo mock interaction
    mockInteraction = {
      user: {
        tag: 'user#1234',
        id: 'user123'
      },
      member: {
        id: 'member123'
      },
      guild: {
        id: 'guild123'
      },
      fields: {
        getTextInputValue: jest.fn()
      },
      deferReply: jest.fn().mockResolvedValue(true),
      editReply: jest.fn().mockResolvedValue(true),
      reply: jest.fn().mockResolvedValue(true),
      deferred: false,
      replied: false
    };
  });

  describe('module registration', () => {
    it('should register modal handler with client', () => {
      // Act
      const modalEventHandler = require('../../../../src/features/verify/events/verify_modal_event');
      modalEventHandler(mockClient);

      // Assert
      expect(mockClient.modalHandlers.set).toHaveBeenCalledWith('verify', expect.any(Function));
      expect(logger.info).toHaveBeenCalledWith(expect.stringContaining('Đã đăng ký handler'));
    });
  });

  describe('handleVerifyModal', () => {
    // Lấy handler function để test trực tiếp
    let handleVerifyModal;

    beforeEach(() => {
      // Lấy handler function từ module
      const modalEventHandler = require('../../../../src/features/verify/events/verify_modal_event');
      modalEventHandler(mockClient);

      // Lấy function đã được đăng ký
      handleVerifyModal = mockClient.modalHandlers.set.mock.calls[0][1];

      // Setup mock cho các trường input
      mockInteraction.fields.getTextInputValue.mockImplementation((fieldId) => {
        if (fieldId === 'email_input') return '<EMAIL>';
        if (fieldId === 'code_input') return 'ABC1234';
        if (fieldId === 'parents_phone_input') return '0987654321';
        return '';
      });

      // Setup mock cho verifyService
      verifyService.verifyStudent.mockResolvedValue({
        success: true,
        message: 'Xác thực thành công',
        courses: ['Hóa học cơ bản (Standard)'],
        studentName: 'Nguyễn Văn A',
        nicknameUpdated: true
      });

      verifyService.createVerificationResultEmbed.mockReturnValue({
        embed: {
          addFields: mockAddFields
        },
        actionRow: 'mockActionRow'
      });
    });

    it('should do nothing for non-modal actions', async () => {
      // Act
      await handleVerifyModal(mockInteraction, 'other', ['submit']);

      // Assert
      expect(mockInteraction.deferReply).not.toHaveBeenCalled();
      expect(verifyService.verifyStudent).not.toHaveBeenCalled();
    });

    it('should do nothing for non-submit params', async () => {
      // Act
      await handleVerifyModal(mockInteraction, 'modal', ['other']);

      // Assert
      expect(mockInteraction.deferReply).not.toHaveBeenCalled();
      expect(verifyService.verifyStudent).not.toHaveBeenCalled();
    });

    it('should validate email format', async () => {
      // Arrange
      mockInteraction.fields.getTextInputValue.mockImplementation((fieldId) => {
        if (fieldId === 'email_input') return 'invalid-email';
        if (fieldId === 'code_input') return 'ABC1234';
        return '';
      });

      // Act
      await handleVerifyModal(mockInteraction, 'modal', ['submit']);

      // Assert
      expect(mockInteraction.deferReply).toHaveBeenCalled();
      expect(logger.warn).toHaveBeenCalledWith(expect.stringContaining('Email không hợp lệ'));
      expect(mockInteraction.editReply).toHaveBeenCalledWith(expect.objectContaining({
        embeds: expect.arrayContaining([
          expect.objectContaining({
            title: '❌ Thông tin không hợp lệ',
            description: expect.stringContaining('Email không đúng định dạng')
          })
        ])
      }));
      expect(verifyService.verifyStudent).not.toHaveBeenCalled();
    });

    it('should validate activation code format', async () => {
      // Arrange
      mockInteraction.fields.getTextInputValue.mockImplementation((fieldId) => {
        if (fieldId === 'email_input') return '<EMAIL>';
        if (fieldId === 'code_input') return 'invalid';
        if (fieldId === 'parents_phone_input') return '0987654321';
        return '';
      });

      // Act
      await handleVerifyModal(mockInteraction, 'modal', ['submit']);

      // Assert
      expect(mockInteraction.deferReply).toHaveBeenCalled();
      expect(logger.warn).toHaveBeenCalledWith(expect.stringContaining('Mã kích hoạt không hợp lệ'));
      expect(mockInteraction.editReply).toHaveBeenCalledWith(expect.objectContaining({
        embeds: expect.arrayContaining([
          expect.objectContaining({
            title: '❌ Thông tin không hợp lệ',
            description: expect.stringContaining('Mã kích hoạt phải có 7 ký tự')
          })
        ])
      }));
      expect(verifyService.verifyStudent).not.toHaveBeenCalled();
    });

    it('should validate parents phone number format', async () => {
      // Arrange
      mockInteraction.fields.getTextInputValue.mockImplementation((fieldId) => {
        if (fieldId === 'email_input') return '<EMAIL>';
        if (fieldId === 'code_input') return 'ABC1234';
        if (fieldId === 'parents_phone_input') return 'invalid123'; // Số điện thoại không hợp lệ
        return '';
      });

      // Act
      await handleVerifyModal(mockInteraction, 'modal', ['submit']);

      // Assert
      expect(mockInteraction.deferReply).toHaveBeenCalled();
      expect(logger.warn).toHaveBeenCalledWith(expect.stringContaining('SĐT phụ huynh không hợp lệ'));
      expect(mockInteraction.editReply).toHaveBeenCalledWith(expect.objectContaining({
        embeds: expect.arrayContaining([
          expect.objectContaining({
            title: '❌ Thông tin không hợp lệ',
            description: expect.stringContaining('Số điện thoại phụ huynh phải là 10 chữ số')
          })
        ])
      }));
      expect(verifyService.verifyStudent).not.toHaveBeenCalled();
    });

    it('should process successful verification', async () => {
      // Act
      await handleVerifyModal(mockInteraction, 'modal', ['submit']);

      // Assert
      expect(mockInteraction.deferReply).toHaveBeenCalledWith({ ephemeral: true });
      expect(verifyService.verifyStudent).toHaveBeenCalledWith(
        '<EMAIL>',
        'ABC1234',
        mockInteraction.user,
        mockInteraction.member,
        mockInteraction.guild,
        '0987654321'
      );
      expect(verifyService.createVerificationResultEmbed).toHaveBeenCalledWith(
        true,
        'Xác thực thành công',
        ['Hóa học cơ bản (Standard)'],
        'Nguyễn Văn A'
      );
      expect(mockInteraction.editReply).toHaveBeenCalledWith({
        embeds: [expect.any(Object)],
        components: ['mockActionRow'],
        ephemeral: true
      });
      expect(logger.info).toHaveBeenCalledWith(expect.stringContaining('Xác thực thành công'));
    });

    it('should handle failed verification', async () => {
      // Arrange
      verifyService.verifyStudent.mockResolvedValue({
        success: false,
        message: 'Email hoặc mã kích hoạt không chính xác'
      });

      // Act
      await handleVerifyModal(mockInteraction, 'modal', ['submit']);

      // Assert
      expect(verifyService.createVerificationResultEmbed).toHaveBeenCalledWith(
        false,
        'Email hoặc mã kích hoạt không chính xác'
      );
      expect(mockInteraction.editReply).toHaveBeenCalledWith(expect.objectContaining({
        components: [expect.any(Object)]
      }));
      expect(logger.info).toHaveBeenCalledWith(expect.stringContaining('Xác thực thất bại'));
    });

    it('should add failed courses field when some courses failed', async () => {
      // Arrange
      verifyService.verifyStudent.mockResolvedValue({
        success: true,
        message: 'Xác thực thành công',
        courses: ['Hóa học cơ bản (Standard)'],
        failedCourses: ['Hóa học nâng cao (Premium)'],
        studentName: 'Nguyễn Văn A',
        nicknameUpdated: true
      });

      // Act
      await handleVerifyModal(mockInteraction, 'modal', ['submit']);

      // Assert
      expect(mockAddFields).toHaveBeenCalledWith({
        name: '⚠️ Lưu ý: Các khóa học chưa được cấp quyền',
        value: 'Hóa học nâng cao (Premium)'
      });
    });

    it('should add nickname note when nickname not updated', async () => {
      // Arrange
      verifyService.verifyStudent.mockResolvedValue({
        success: true,
        message: 'Xác thực thành công',
        courses: ['Hóa học cơ bản (Standard)'],
        studentName: 'Nguyễn Văn A',
        nicknameUpdated: false
      });

      // Act
      await handleVerifyModal(mockInteraction, 'modal', ['submit']);

      // Assert
      expect(mockAddFields).toHaveBeenCalledWith({
        name: '📝 Lưu ý về tên hiển thị',
        value: expect.stringContaining('Bot không thể tự động đổi tên hiển thị')
      });
    });

    it('should handle errors during processing', async () => {
      // Arrange
      const error = new Error('Test error');
      verifyService.verifyStudent.mockRejectedValue(error);
      mockInteraction.deferred = true;

      // Act
      await handleVerifyModal(mockInteraction, 'modal', ['submit']);

      // Assert
      expect(logger.error).toHaveBeenCalledWith(
        'Lỗi khi xử lý modal xác thực:',
        expect.objectContaining({
          message: error.message,
          stack: error.stack
        })
      );
      expect(mockInteraction.editReply).toHaveBeenCalledWith(expect.objectContaining({
        embeds: [expect.objectContaining({
          title: '❌ Đã xảy ra lỗi'
        })]
      }));
    });

    it('should use reply if not deferred', async () => {
      // Arrange
      const error = new Error('Test error');
      mockInteraction.deferReply.mockRejectedValue(error);
      mockInteraction.deferred = false;

      // Act
      await handleVerifyModal(mockInteraction, 'modal', ['submit']);

      // Assert
      expect(logger.error).toHaveBeenCalled();
      expect(mockInteraction.reply).toHaveBeenCalledWith(expect.objectContaining({
        embeds: [expect.objectContaining({
          title: '❌ Đã xảy ra lỗi'
        })]
      }));
    });

    it('should handle errors when replying with error message', async () => {
      // Arrange
      const error = new Error('Test error');
      const replyError = new Error('Reply error');

      verifyService.verifyStudent.mockRejectedValue(error);
      mockInteraction.deferred = true;
      mockInteraction.editReply.mockRejectedValue(replyError);

      // Act
      await handleVerifyModal(mockInteraction, 'modal', ['submit']);

      // Assert
      expect(logger.error).toHaveBeenCalledWith(
        'Lỗi khi xử lý modal xác thực:',
        expect.objectContaining({
          message: error.message
        })
      );

      // Kiểm tra lỗi khi gửi thông báo
      expect(logger.error).toHaveBeenCalledWith(
        'Không thể gửi thông báo lỗi:',
        expect.any(String)
      );
    });
  });
});
