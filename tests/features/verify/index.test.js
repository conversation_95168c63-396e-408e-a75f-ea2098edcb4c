/**
 * @file index.test.js
 * @description Unit tests cho module index của t<PERSON>h năng verify
 * <AUTHOR> Assistant
 * @date 2025-05-09
 */

// Mock các dependency
jest.mock('../../../src/features/verify/commands/verify_command', () => 'mockVerifyCommand');
jest.mock('../../../src/features/verify/events/verify_button_event', () => jest.fn());
jest.mock('../../../src/features/verify/events/verify_modal_event', () => jest.fn());
jest.mock('../../../src/shared/utils/logger', () => ({
  info: jest.fn()
}));

// Import các module cần test
const verifyFeature = require('../../../src/features/verify/index');
const verifyButtonHandler = require('../../../src/features/verify/events/verify_button_event');
const verifyModalHandler = require('../../../src/features/verify/events/verify_modal_event');
const logger = require('../../../src/shared/utils/logger');

describe('Verify Feature Index', () => {
  // Tạo mock client
  let mockClient;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Tạo mock client
    mockClient = {};
  });

  describe('module structure', () => {
    it('should export commands array', () => {
      // Assert
      expect(verifyFeature.commands).toBeInstanceOf(Array);
      expect(verifyFeature.commands).toContain('mockVerifyCommand');
    });

    it('should export init function', () => {
      // Assert
      expect(verifyFeature.init).toBeInstanceOf(Function);
    });

    it('should export feature info', () => {
      // Assert
      expect(verifyFeature.info).toBeInstanceOf(Object);
      expect(verifyFeature.info.name).toBe('Verify');
      expect(verifyFeature.info.description).toContain('xác thực học sinh');
      expect(verifyFeature.info.version).toBe('1.0.0');
    });
  });

  describe('init function', () => {
    it('should initialize all handlers', () => {
      // Act
      verifyFeature.init(mockClient);

      // Assert
      expect(verifyButtonHandler).toHaveBeenCalledWith(mockClient);
      expect(verifyModalHandler).toHaveBeenCalledWith(mockClient);
      expect(logger.info).toHaveBeenCalledWith(expect.stringContaining('Tính năng verify đã được khởi tạo'));
    });
  });
});
