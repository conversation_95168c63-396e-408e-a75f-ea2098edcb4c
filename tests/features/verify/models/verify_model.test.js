/*
 * @file verify_model.test.js
 * @description Unit tests cho verify_model.js b<PERSON> gồm verifyStudent, checkEmailUsedByOtherDiscord, getStudentCourses, markActivationAsUsed
 * <AUTHOR> Assistant
 * @date 2025-05-21
 */

jest.mock('../../../../src/shared/database/connection', () => ({
  query: jest.fn(),
  getPool: jest.fn()
}));
jest.mock('../../../../src/shared/utils/logger', () => ({
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn()
}));

const db = require('../../../../src/shared/database/connection');
const logger = require('../../../../src/shared/utils/logger');
const {
  verifyStudent,
  checkEmailUsedByOtherDiscord,
  getStudentCourses,
  markActivationAsUsed
} = require('../../../../src/features/verify/models/verify_model');

describe('verify_model', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('verifyStudent', () => {
    const email = '<EMAIL>';
    const code = 'ABC123';
    const FEATURE = 'verify';

    it('should return null if no rows found', async () => {
      db.query.mockResolvedValue([[], undefined]);
      const result = await verifyStudent(email, code);
      expect(db.query).toHaveBeenCalledWith(expect.any(String), [email, code], FEATURE);
      expect(logger.info).toHaveBeenCalledWith(expect.stringContaining('Không tìm thấy thông tin xác thực'));
      expect(result).toBeNull();
    });

    it('should return row if found', async () => {
      const row = { user_id: 1, email, code };
      db.query.mockResolvedValue([[row], undefined]);
      const result = await verifyStudent(email, code);
      expect(result).toEqual(row);
    });

    it('should throw and log error on failure', async () => {
      const error = new Error('DB error');
      db.query.mockRejectedValue(error);
      await expect(verifyStudent(email, code)).rejects.toThrow(error);
      expect(logger.error).toHaveBeenCalledWith(
        'Lỗi kiểm tra thông tin xác thực:',
        expect.objectContaining({ message: error.message })
      );
    });
  });

  describe('checkEmailUsedByOtherDiscord', () => {
    const email = '<EMAIL>';
    const FEATURE = 'verify';

    it('should return true when count > 0', async () => {
      db.query.mockResolvedValue([[{ count: 2 }], undefined]);
      const result = await checkEmailUsedByOtherDiscord(email);
      expect(db.query).toHaveBeenCalledWith(expect.any(String), [email], FEATURE);
      expect(result).toBe(true);
    });

    it('should return false when count = 0', async () => {
      db.query.mockResolvedValue([[{ count: 0 }], undefined]);
      const result = await checkEmailUsedByOtherDiscord(email);
      expect(result).toBe(false);
    });

    it('should throw and log error on failure', async () => {
      const error = new Error('Query error');
      db.query.mockRejectedValue(error);
      await expect(checkEmailUsedByOtherDiscord(email)).rejects.toThrow(error);
      expect(logger.error).toHaveBeenCalledWith(
        'Lỗi kiểm tra email đã sử dụng:',
        expect.objectContaining({ message: error.message })
      );
    });
  });

  describe('getStudentCourses', () => {
    const email = '<EMAIL>';
    const FEATURE = 'verify';

    it('should return empty array if no courses', async () => {
      db.query.mockResolvedValue([[], undefined]);
      const result = await getStudentCourses(email);
      expect(db.query).toHaveBeenCalledWith(expect.any(String), [email], FEATURE);
      expect(logger.info).toHaveBeenCalledWith(expect.stringContaining('Không tìm thấy khóa học nào'));
      expect(result).toEqual([]);
    });

    it('should return rows if courses found', async () => {
      const rows = [{ course_id: 1 }, { course_id: 2 }];
      db.query.mockResolvedValue([rows, undefined]);
      const result = await getStudentCourses(email);
      expect(result).toEqual(rows);
    });

    it('should throw and log error on failure', async () => {
      const error = new Error('DB fail');
      db.query.mockRejectedValue(error);
      await expect(getStudentCourses(email)).rejects.toThrow(error);
      expect(logger.error).toHaveBeenCalledWith(
        'Lỗi lấy danh sách khóa học của học sinh:',
        expect.objectContaining({ message: error.message })
      );
    });
  });

  describe('markActivationAsUsed', () => {
    const activationCodeId = 1;
    const discordUserId = 'discord123';
    const discordUsername = 'user#1234';
    const parentsPhone = '0987654321';
    let connection;

    beforeEach(() => {
      connection = {
        beginTransaction: jest.fn(),
        query: jest.fn(),
        commit: jest.fn(),
        rollback: jest.fn(),
        release: jest.fn()
      };
      db.getPool.mockReturnValue({ getConnection: jest.fn().mockResolvedValue(connection) });
    });

    it('should return true on successful update', async () => {
      connection.query
        .mockImplementation((query, params) => {
          if (query.includes('SELECT u.id, u.email')) {
            return Promise.resolve([[{ id: 10, email: '<EMAIL>' }], undefined]);
          } else if (query.includes('UPDATE activation_codes')) {
            return Promise.resolve([{ affectedRows: 1 }]);
          } else if (query.includes('UPDATE up_users')) {
            return Promise.resolve([{ affectedRows: 1 }]);
          } else if (query.includes("SET time_zone")) {
            return Promise.resolve([]);
          }
          return Promise.reject(new Error('Unexpected query: ' + query));
        });
      const result = await markActivationAsUsed(activationCodeId, discordUserId, discordUsername, parentsPhone);
      expect(connection.beginTransaction).toHaveBeenCalled();
      expect(connection.commit).toHaveBeenCalled();
      expect(connection.release).toHaveBeenCalled();
      expect(logger.info).toHaveBeenCalledWith(expect.stringContaining('Đã cập nhật user_id'));
      expect(result).toBe(true);
    });

    it('should return false if userInfo not found', async () => {
      connection.query.mockImplementation((query, params) => {
        if (query.includes('SELECT u.id, u.email')) {
          return Promise.resolve([[], undefined]);
        } else if (query.includes("SET time_zone")) {
          return Promise.resolve([]);
        }
        return Promise.reject(new Error('Unexpected query: ' + query));
      });
      const result = await markActivationAsUsed(activationCodeId, discordUserId, discordUsername, parentsPhone);
      expect(connection.rollback).toHaveBeenCalled();
      expect(connection.release).toHaveBeenCalled();
      expect(logger.warn).toHaveBeenCalledWith(expect.stringContaining('Không thể tìm thấy thông tin người dùng'));
      expect(result).toBe(false);
    });

    it('should return false if activation update affectedRows = 0', async () => {
      connection.query.mockImplementation((query, params) => {
        if (query.includes('SELECT u.id, u.email')) {
          return Promise.resolve([[{ id: 10, email: '<EMAIL>' }], undefined]);
        } else if (query.includes('UPDATE activation_codes')) {
          return Promise.resolve([{ affectedRows: 0 }]);
        } else if (query.includes("SET time_zone")) {
          return Promise.resolve([]);
        }
        return Promise.reject(new Error('Unexpected query: ' + query));
      });
      const result = await markActivationAsUsed(activationCodeId, discordUserId, discordUsername, parentsPhone);
      expect(connection.rollback).toHaveBeenCalled();
      expect(connection.release).toHaveBeenCalled();
      expect(logger.warn).toHaveBeenCalledWith(expect.stringContaining('Không thể cập nhật trạng thái'));
      expect(result).toBe(false);
    });

    it('should throw error and rollback on inner query failure', async () => {
      const innerError = new Error('Inner query failed');
      connection.query.mockRejectedValue(innerError);
      await expect(markActivationAsUsed(activationCodeId, discordUserId, discordUsername, parentsPhone)).rejects.toThrow(innerError);
      expect(connection.rollback).toHaveBeenCalled();
      expect(connection.release).toHaveBeenCalled();
    });

    it('should throw error and log on connection failure', async () => {
      const poolError = new Error('Pool error');
      db.getPool.mockImplementation(() => { throw poolError; });
      await expect(markActivationAsUsed(activationCodeId, discordUserId, discordUsername, parentsPhone)).rejects.toThrow(poolError);
      expect(logger.error).toHaveBeenCalledWith(
        'Lỗi đánh dấu mã kích hoạt đã sử dụng:',
        expect.objectContaining({ message: poolError.message })
      );
    });
  });
});