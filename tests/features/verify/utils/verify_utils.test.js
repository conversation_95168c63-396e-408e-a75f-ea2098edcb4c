/**
 * @file verify_utils.test.js
 * @description Unit tests cho các hàm tiện ích của tính năng xác thực (verify)
 * <AUTHOR> Assistant
 * @date 2025-05-09
 */

// Mock các dependency từ discord.js
const mockAddComponents = jest.fn().mockReturnThis();
const mockActionRowBuilder = jest.fn().mockImplementation(() => ({
  addComponents: mockAddComponents
}));

const mockSetCustomId = jest.fn().mockReturnThis();
const mockSetLabel = jest.fn().mockReturnThis();
const mockSetStyle = jest.fn().mockReturnThis();
const mockSetEmoji = jest.fn().mockReturnThis();
const mockSetURL = jest.fn().mockReturnThis();

const mockButtonBuilder = jest.fn().mockImplementation(() => ({
  setCustomId: mockSetCustomId,
  setLabel: mockSetLabel,
  setStyle: mockSetStyle,
  setEmoji: mockSetEmoji,
  setURL: mockSetURL
}));

const mockSetPlaceholder = jest.fn().mockReturnThis();
const mockSetRequired = jest.fn().mockReturnThis();
const mockSetMinLength = jest.fn().mockReturnThis();
const mockSetMaxLength = jest.fn().mockReturnThis();

const mockTextInputBuilder = jest.fn().mockImplementation(() => ({
  setCustomId: mockSetCustomId,
  setLabel: mockSetLabel,
  setPlaceholder: mockSetPlaceholder,
  setStyle: mockSetStyle,
  setRequired: mockSetRequired,
  setMinLength: mockSetMinLength,
  setMaxLength: mockSetMaxLength
}));

const mockSetTitle = jest.fn().mockReturnThis();
const mockModalBuilder = jest.fn().mockImplementation(() => ({
  setCustomId: mockSetCustomId,
  setTitle: mockSetTitle,
  addComponents: mockAddComponents
}));

// Mock discord.js
jest.mock('discord.js', () => ({
  ActionRowBuilder: mockActionRowBuilder,
  ModalBuilder: mockModalBuilder,
  TextInputBuilder: mockTextInputBuilder,
  TextInputStyle: {
    Short: 1,
    Paragraph: 2
  },
  ButtonBuilder: mockButtonBuilder,
  ButtonStyle: {
    Primary: 1,
    Secondary: 2,
    Success: 3,
    Danger: 4,
    Link: 5
  }
}));

// Import module cần test
const verifyUtils = require('../../../../src/features/verify/utils/verify_utils');

describe('Verify Utils', () => {
  // Reset mocks trước mỗi test
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('createVerifyButton', () => {
    it('should create an action row with verify and support buttons', () => {
      // Act
      const result = verifyUtils.createVerifyButton();

      // Assert
      // Kiểm tra ActionRowBuilder được gọi
      expect(mockActionRowBuilder).toHaveBeenCalledTimes(1);

      // Kiểm tra ButtonBuilder được gọi 2 lần (verify và support)
      expect(mockButtonBuilder).toHaveBeenCalledTimes(2);

      // Kiểm tra cấu hình nút verify
      expect(mockSetCustomId).toHaveBeenCalledWith('verify:button:verify');
      expect(mockSetLabel).toHaveBeenCalledWith('Xác thực tài khoản');
      expect(mockSetStyle).toHaveBeenCalledWith(1); // ButtonStyle.Primary
      expect(mockSetEmoji).toHaveBeenCalledWith('✅');

      // Kiểm tra cấu hình nút support
      expect(mockSetLabel).toHaveBeenCalledWith('Hỗ trợ qua Zalo');
      expect(mockSetStyle).toHaveBeenCalledWith(5); // ButtonStyle.Link
      expect(mockSetURL).toHaveBeenCalledWith('https://zalo.me/0828949479');
      expect(mockSetEmoji).toHaveBeenCalledWith('💬');

      // Kiểm tra addComponents được gọi với 2 nút
      expect(mockAddComponents).toHaveBeenCalledTimes(1);

      // Kiểm tra kết quả trả về
      expect(result).toBeDefined();
    });
  });

  describe('createVerifyModal', () => {
    it('should create a modal with email, code, and parents phone inputs', () => {
      // Act
      const result = verifyUtils.createVerifyModal();

      // Assert
      // Kiểm tra ModalBuilder được gọi
      expect(mockModalBuilder).toHaveBeenCalledTimes(1);

      // Kiểm tra TextInputBuilder được gọi 3 lần (email, code và parents phone)
      expect(mockTextInputBuilder).toHaveBeenCalledTimes(3);

      // Kiểm tra ActionRowBuilder được gọi 3 lần (email row, code row và parents phone row)
      expect(mockActionRowBuilder).toHaveBeenCalledTimes(3);

      // Kiểm tra cấu hình modal
      expect(mockSetCustomId).toHaveBeenCalledWith('verify:modal:submit');
      expect(mockSetTitle).toHaveBeenCalledWith('Xác thực tài khoản');

      // Kiểm tra cấu hình email input
      expect(mockSetCustomId).toHaveBeenCalledWith('email_input');
      expect(mockSetLabel).toHaveBeenCalledWith('Email đã dùng để mua khóa học');
      expect(mockSetPlaceholder).toHaveBeenCalledWith('<EMAIL>');
      expect(mockSetRequired).toHaveBeenCalledWith(true);
      expect(mockSetMinLength).toHaveBeenCalledWith(5);
      expect(mockSetMaxLength).toHaveBeenCalledWith(100);

      // Kiểm tra cấu hình code input
      expect(mockSetCustomId).toHaveBeenCalledWith('code_input');
      expect(mockSetLabel).toHaveBeenCalledWith('Mã kích hoạt');
      expect(mockSetPlaceholder).toHaveBeenCalledWith('ABC1234 (7 ký tự A-Z, 0-9 in hoa)');
      expect(mockSetRequired).toHaveBeenCalledWith(true);
      expect(mockSetMinLength).toHaveBeenCalledWith(7);
      expect(mockSetMaxLength).toHaveBeenCalledWith(7);

      // Kiểm tra cấu hình parents phone input
      expect(mockSetCustomId).toHaveBeenCalledWith('parents_phone_input');
      expect(mockSetLabel).toHaveBeenCalledWith('Số điện thoại phụ huynh (10 số)');
      expect(mockSetPlaceholder).toHaveBeenCalledWith('VD: 09XXXXXXXX');
      expect(mockSetRequired).toHaveBeenCalledWith(true);
      expect(mockSetMinLength).toHaveBeenCalledWith(10);
      expect(mockSetMaxLength).toHaveBeenCalledWith(10);

      // Kiểm tra kết quả trả về
      expect(result).toBeDefined();
    });
  });

  describe('getSuccessMessage', () => {
    it('should return success message with student name when provided', () => {
      // Arrange
      const studentName = 'Nguyễn Văn A';

      // Act
      const result = verifyUtils.getSuccessMessage(studentName);

      // Assert
      expect(result).toContain('✅ Xác thực thành công!');
      expect(result).toContain(`Chào mừng **${studentName}**!`);
      expect(result).toContain('Kiểm tra thanh bên trái');
      expect(result).toContain('Chúc bạn học tập hiệu quả!');
    });

    it('should return success message without student name when not provided', () => {
      // Act
      const result = verifyUtils.getSuccessMessage();

      // Assert
      expect(result).toContain('✅ Xác thực thành công!');
      expect(result).not.toContain('Chào mừng **!');
      expect(result).toContain('Chúc mừng! Bạn đã xác thực thành công');
      expect(result).toContain('Kiểm tra thanh bên trái');
      expect(result).toContain('Chúc bạn học tập hiệu quả!');
    });
  });

  describe('getErrorMessage', () => {
    it('should return error message with the provided error message', () => {
      // Arrange
      const errorMessage = 'Email hoặc mã kích hoạt không chính xác';

      // Act
      const result = verifyUtils.getErrorMessage(errorMessage);

      // Assert
      expect(result).toContain('❌ Xác thực không thành công');
      expect(result).toContain(errorMessage);
      expect(result).toContain('Nếu bạn cần hỗ trợ');
      expect(result).toContain('https://zalo.me/0828949479');
    });
  });
});
