/**
 * @file setup.js
 * @description Setup file cho Jest, mock các module không cần thiết khi chạy test
 * <AUTHOR> Assistant
 * @date 2025-05-09
 */

// Mock .env
jest.mock('dotenv', () => ({
  config: jest.fn()
}));

// Mock database configuration
jest.mock('../src/core/config', () => ({
  database: {
    verify: {
      host: 'mock-host',
      port: 3306,
      user: 'mock-user',
      password: 'mock-password',
      database: 'mock-database'
    }
  },
  bot: {
    token: 'mock-token',
    clientId: 'mock-client-id',
    guildId: 'mock-guild-id'
  }
}));

// Mock logger
jest.mock('../src/shared/utils/logger', () => ({
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  debug: jest.fn()
}));

// Mock database connection
jest.mock('../src/shared/database/connection', () => ({
  query: jest.fn(),
  getPool: jest.fn().mockReturnValue({
    getConnection: jest.fn().mockResolvedValue({
      query: jest.fn(),
      beginTransaction: jest.fn(),
      commit: jest.fn(),
      rollback: jest.fn(),
      release: jest.fn()
    })
  })
}));

// Mock discord.js
jest.mock('discord.js', () => ({
  EmbedBuilder: jest.fn().mockImplementation(() => ({
    setTitle: jest.fn().mockReturnThis(),
    setDescription: jest.fn().mockReturnThis(),
    setColor: jest.fn().mockReturnThis(),
    setTimestamp: jest.fn().mockReturnThis(),
    addFields: jest.fn().mockReturnThis(),
    data: {
      title: '',
      description: '',
      color: 0,
      fields: undefined
    }
  }))
})); 